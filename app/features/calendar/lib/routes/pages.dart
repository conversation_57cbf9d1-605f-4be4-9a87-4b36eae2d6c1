import 'package:gp_core/routes/router_name.dart';
import 'package:gp_core/utils/dio_wrapper.dart';
import 'package:gp_feat_calendar/screens/collab/collab_calendar_page.dart';
import 'package:gp_feat_calendar/screens/create/calendar_description_screen.dart';
import 'package:gp_feat_calendar/screens/create/calendar_select_room_screen.dart';
import 'package:gp_feat_calendar/screens/create/create_calendar_event_page.dart';
import 'package:gp_feat_calendar/screens/create/custom_repeat/customize_repeat_page.dart';
import 'package:gp_feat_calendar/screens/detail/calendar_event_detail_page.dart';
import 'package:gp_feat_calendar/screens/detail/pages/email_invitees_list_page.dart';
import 'package:gp_feat_calendar/screens/detail/pages/invitees_view_only_list_page.dart';
import 'package:gp_feat_calendar/screens/list/booking_room/change_room_screen.dart';
import 'package:gp_feat_calendar/screens/list/calendar_page.dart';
import 'package:gp_feat_calendar/screens/list_notification/calendar_list_notification_screen.dart';
import 'package:gp_feat_calendar/screens/subscription/subscription_pricing_page.dart';
import 'package:gp_feat_calendar/screens/subscription/subscription_pricing_controller.dart';
import 'package:gp_shared_dep/gp_shared_dep.dart';

import 'router_name.dart';

mixin CalendarPages {
  static List<GetPage> pages = [
    GetPage(
      name: CalendarRouterName.createCalendarEvent,
      page: () => const CreateCalendarEventPage(),
      binding: CreateCalendarEventBindings(),
    ),
    GetPage(
      name: RouterName.routeWithoutAnimation(
          CalendarRouterName.createCalendarEvent),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: CreateCalendarEventPage()),
      binding: CreateCalendarEventBindings(),
    ),
    GetPage(
      name: CalendarRouterName.calendarDescription,
      page: () => const CalendarDescriptionScreen(),
      binding: CalendarDescriptionScreenBinding(),
    ),
    GetPage(
      name: CalendarRouterName.calendarRoomSelect,
      page: () => const CalendarSelectRoomScreen(),
      binding: CalendarSelectRoomScreenBinding(),
    ),
    GetPage(
      name: CalendarRouterName.calendar,
      transitionDuration: Duration.zero,
      page: () => const CalendarPage(),
      binding: CalendarBinding(),
    ),
    GetPage(
      name: RouterName.routeWithoutAnimation(CalendarRouterName.calendar),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: CalendarPage()),
      binding: CalendarBinding(),
    ),
    GetPage(
      name: CalendarRouterName.calendarCollab,
      transitionDuration: Duration.zero,
      page: () => const CollabCalendarPage(),
      binding: CollabCalendarBinding(),
    ),
    GetPage(
      name: RouterName.routeWithoutAnimation(CalendarRouterName.calendarCollab),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: CollabCalendarPage()),
      binding: CollabCalendarBinding(),
    ),
    GetPage(
      name: CalendarRouterName.calendarEventDetail,
      page: () => const CalendarEventDetailPage(),
      binding: CalendarEventDetailBindings(),
    ),
    GetPage(
      name: RouterName.routeWithoutAnimation(
          CalendarRouterName.calendarEventDetail),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: CalendarEventDetailPage()),
      binding: CalendarEventDetailBindings(),
    ),
    GetPage(
        name: CalendarRouterName.calendarNotifications,
        page: () => const CalendarListNotificationScreen(),
        binding: CalendarListNotificationBinding()),
    GetPage(
      name: CalendarRouterName.calendarRepeatCustomize,
      page: () => const CalendarCustomRepeatPage(),
      binding: CustomRepeatBindings(),
    ),
    GetPage(
        name: RouterName.routeWithoutAnimation(
            CalendarRouterName.calendarNotifications),
        transitionDuration: Duration.zero,
        page: () =>
            const DioLogWrapperWidget(child: CalendarListNotificationScreen()),
        binding: CalendarListNotificationBinding()),
    GetPage(
      name: CalendarRouterName.listInvitees,
      page: () => const InviteesViewOnlyListPage(),
      binding: InviteesViewOnlyListBindings(),
    ),
    GetPage(
      name: CalendarRouterName.listEmailInvitees,
      page: () => const EmailInviteesListPage(),
      binding: EmailInviteesListBindings(),
    ),
    GetPage(
      name: CalendarRouterName.changeRoomBooking,
      page: () => const ChangeRoomBookingPage(),
      binding: ChangeRoomBookingBinding(),
    ),
    GetPage(
      name: CalendarRouterName.subscriptionPricing,
      page: () => const SubscriptionPricingPage(),
      binding: SubscriptionPricingBinding(),
    ),
    GetPage(
      name: RouterName.routeWithoutAnimation(
          CalendarRouterName.subscriptionPricing),
      transitionDuration: Duration.zero,
      page: () => const DioLogWrapperWidget(child: SubscriptionPricingPage()),
      binding: SubscriptionPricingBinding(),
    )
  ];
}
