// ignore_for_file: library_private_types_in_public_api

import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/navigator/platform_navigator.dart';
import 'package:gp_feat_calendar/models/assignee.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_calendar/screens/collab/collab_calendar_controller.dart';
import 'package:gp_feat_calendar/screens/create/components/popup/popup_google_scope.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_custom_event_type.dart';
import 'package:gp_feat_calendar/screens/list/controller.dart';
import 'package:gp_feat_calendar/screens/list/ext/google_controller.ext.dart';
import 'package:gp_feat_calendar/screens/list/ext/google_signin.dart';
import 'package:gp_feat_calendar/screens/list/models/calendar_collab_input.dart';
import 'package:gp_feat_calendar/screens/list/models/event_list_response.dart';
import 'package:gp_feat_calendar/screens/list/models/event_room.dart';
import 'package:gp_feat_calendar/services/calendar_api.dart';

import 'calendar_description_controller.dart';
import 'components/calendar_attachment_files_controller.dart';
import 'components/calendar_choose_edit_method_popup.dart';
import 'components/calendar_datetime_picker.dart';
import 'models/calendar_datetime_picker_result.dart';
import 'models/calendar_event_remind_option.dart';
import 'models/calendar_event_repeat_option.dart';
import 'models/calendar_event_schedule_type.dart';
import 'models/calendar_event_type.dart';
import 'models/create_calendar_event_request.dart';
import 'models/create_calendar_event_schedule_request.dart';
import 'models/default_create_calendar_event_request.dart';

import 'package:diffutil_dart/diffutil.dart';

enum CreateCalendarResultEnum {
  noResult,
  createdNewEventWithResultIsNewEvent,
  createdNewEventWithNoResult,
  edittedEventWithResultIsEdittedEvent,
}

class CreateCalendarResult {
  final CreateCalendarResultEnum resultType;
  final CalendarEventList? result;
  final String? googleEmail;

  CreateCalendarResult({
    required this.resultType,
    this.result,
    this.googleEmail,
  });
}

class CreateCalendarEventController extends BaseController
    with _CompareCreateRequestComparation, _CustomizeRecurrenceRule, HostMixin {
  TextEditingController titleController = TextEditingController();

  CalendarDateTimePickerController calendarController =
      CalendarDateTimePickerController();

  late CalendarAttachmentFilesController attachmentFilesController;

  RxList<Assignee> selectedAssignees = RxList();
  RxList<int> selectedAssigneeIds = RxList();
  RxList<OrganizationDepartment> selectedDepartments = RxList();
  RxList<OrganizationRole> selectedRoles = RxList();
  RxList<String> selectedDepsAndRoles = RxList();
  RxList<Conversation> selectedThreads = RxList();
  RxList<Participant> ignoreMembers = RxList();
  RxList<String> emailGuests = RxList();
  RxList<EventRoom> rooms = RxList();

  RxString ownerAvatarUrl = ''.obs;
  RxString ownerDisplayName = ''.obs;

  // Host properties
  RxList<int> selectedHostIds = RxList();

  // default hosts khi fetch event
  List<Assignee> hosts = [];
  List<int> removeHostOnEditInvitee = [];
  RxList<Assignee> selectedHosts = RxList();

  HostDataHandlerModel? hostDataHandlerModel;

  RxBool hasSelectTypeUI = true.obs;

  RxBool saveButtonEnabled = true.obs;
  RxBool allday = false.obs;

  RxBool hasMeet = true.obs;
  RxBool hasAIAssistant = false.obs;
  RxString description = ''.obs;
  Rx<CalendarEventRemindOption> selectedRemindOption =
      CalendarEventRemindOption.options[2].obs;
  Rx<CalendarEventRepeatOption> selectedRepeatOption =
      CalendarEventRepeatOption.noRepeat.obs;

  Rx<CalendarEventType> selectedCalendarEventType =
      CalendarEventType.meeting.obs;

  // Custom event types
  RxList<CalendarCustomEventType> customEventTypes =
      RxList<CalendarCustomEventType>();
  Rxn<CalendarCustomEventType> selectedCustomEventType =
      Rxn<CalendarCustomEventType>();

  //
  final meetExpanableController = ExpandableController();
  final attachmentExpanableController = ExpandableController();
  final descriptionExpanableController = ExpandableController();
  final remindExpanableController = ExpandableController();
  final roomBookingExpanableController = ExpandableController();
  final notSyncNotiExpanableController =
      ExpandableController(initialExpanded: false);

  RxBool isNextButtonEnabled = true.obs;

  // TODO: Refactor, đổi qua dùng strategy pattern
  final _calendarService = CalendarAPI();
  // final _meetingCollabAPI = MeetingCollabAPI();

  late DateTime initialDateTime;
  Duration? durationToAdd;

  int startDate = 0;
  int endDate = 0;

  bool get cannotAddRoom =>
      (_editingEvent.value?.isExceptionEvent ?? false) ||
      (selectedRepeatOption.value.optionEnum !=
          CalendarEventRepeatOptionEnum.noRepeat);

  // edit flow variables
  final _editingEvent = Rxn<CalendarEventList>();
  RxBool isEditing = false.obs;

  CreateCalendarEventController({GPConnection? gpConnection})
      : super(gpConnection ?? GPConnectionConcrete());

  bool get _showChooseEditMethod {
    // không hiển thị popup options lưu chỉ khi
    // event 1 lần
    // và event không có root_id hoặc root_id = _id
    if (_editingEvent.value?.rootId?.isEmpty ?? true) {
      return false;
    }
    // khi sửa 1 event lặp với option "chỉ sự kiện này" sẽ sinh ra 1 event mới
    // thì event mới này chính là 1 exception event của chuỗi lặp đấy
    // cứ khi sửa 1 event trong chuỗi lặp (sửa exception event hay event gốc)
    // sẽ đều cần phải hiện lên popup có 3 option lưu
    if (_editingEvent.value?.isExceptionEvent ?? false) {
      return true;
    }
    return selectedRepeatOption.value.toCalendarEventScheduleType() !=
            CalendarEventScheduleType.once &&
        _editingEvent.value?.schedule?.type != CalendarEventScheduleType.once;
  }

  bool get hasSelectedAssignees => selectedAssignees.isNotEmpty;
  bool get hasSelectedDepartments => selectedDepartments.isNotEmpty;
  bool get hasSelectedRoles => selectedRoles.isNotEmpty;
  bool get hasSelectedThreads => selectedThreads.isNotEmpty;
  bool get hasEmails => emailGuests.isNotEmpty;

  List<String> get departmentAndRoleStrs {
    final _departments = selectedDepartments
        .map((e) => e.name)
        // .where((e) => e != null)
        // .map((e) => e as String)
        .toList();
    final _roles = selectedRoles
        .map((e) => e.name)
        .where((e) => e != null)
        .map((e) => e as String)
        .toList();

    return _departments + _roles;
  }

  bool _argumentIsAllDay = false;

  final List<BottomSheetModel> bsmRepeats = [];

  // Collab
  Rxn<CalendarCollabInput> calendarCollabInput = Rxn();
  RxBool isCollabFollow = false.obs;

  int descriptionRtf = 0;
  String googleEmail = '';
  String? collabId;
  bool isDeletingRoom = false;

  /// Tạo từ màn lịch book phòng họp?
  bool isBookingRoom = false;

  int roomApproversPreview = 3;

  bool hasChangeThread = false;
  bool hasChangeDepartment = false;
  bool hasChangeRole = false;

  final tooltipController = SuperTooltipController();
  bool? isSynced;

  bool get showNotSyncNoti =>
      (isSynced == false && googleEmail.isEmpty) ||
      (isSynced == false && googleEmail.isNotEmpty && hasMeet.value == false);

  @override
  void onInit() {
    super.onInit();

    // binding
    calendarCollabInput.stream.listen((event) {
      isCollabFollow.value = event != null;
      hasSelectTypeUI.value = !isCollabFollow.value;
    });

    selectedCalendarEventType.stream.listen((event) {
      final hasMeetUI = event == CalendarEventType.meeting;
      meetExpanableController.expanded = hasMeetUI;

      attachmentExpanableController.expanded =
          event == CalendarEventType.meeting;

      descriptionExpanableController.expanded =
          event != CalendarEventType.reminder;

      remindExpanableController.expanded = event != CalendarEventType.reminder;

      roomBookingExpanableController.expanded =
          event == CalendarEventType.meeting;

      notSyncNotiExpanableController.expanded = hasMeetUI && showNotSyncNoti;
    });

    calendarController.addListener(_onCalendarControllerChanged);

    //
    if (Get.arguments is List) {
      // create flow
      if (Get.arguments[0] is DateTime) {
        initialDateTime = Get.arguments[0];
      }
      if (Get.arguments[1] is CalendarEventType) {
        selectedCalendarEventType.value = Get.arguments[1];
      }
      if (Get.arguments[2] is Duration) {
        durationToAdd = Get.arguments[2];
      }
      if (Get.arguments[3] is bool) {
        _argumentIsAllDay = Get.arguments[3];
      }
      if (Get.arguments.length > 4 && Get.arguments[4] is CalendarCollabInput) {
        calendarCollabInput.value = Get.arguments[4];
        collabId = calendarCollabInput.value?.collabGroupId;
      }
      if (Get.arguments.length > 5 && Get.arguments[5] is String) {
        googleEmail = Get.arguments[5];
      }
      if (Get.arguments.length > 6 && Get.arguments[6] is EventRoom) {
        rooms.add(Get.arguments[6]);
        isBookingRoom = true;
        hasSelectTypeUI.value = false;
      }
      if (Get.arguments.length > 7 && Get.arguments[7] is bool) {
        isSynced = Get.arguments[7];
      }
    } else if (Get.arguments is CalendarEventList) {
      googleEmail = Get.parameters['googleEmail'] ?? '';
      collabId = Get.parameters['collabId'];
      isSynced = Get.parameters['isSynced'] == 'true';
      // edit flow
      _editingEvent.value = Get.arguments;
      isEditing.value = true;

      hasSelectTypeUI.value = false;

      initialDateTime = DateTime.fromMillisecondsSinceEpoch(
          _editingEvent.value?.startAt ?? 0);

      selectedCalendarEventType.value =
          _editingEvent.value?.type ?? CalendarEventType.meeting;
      allday.value = _editingEvent.value?.schedule?.isAllday ?? false;

      isCollabFollow.value =
          _editingEvent.value?.collabGroupId?.isNotEmpty == true;
      if (_editingEvent.value?.rooms?.isNotEmpty ?? false) {
        rooms.addAll(_editingEvent.value?.rooms ?? []);
      }

      hosts.clear();
      hosts.addAll(_editingEvent.value?.hosts ?? []);

      selectedHosts.clear();
      selectedHosts.addAll(_editingEvent.value?.hosts ?? []);

      selectedHostIds.clear();
      selectedHostIds.addAll(_editingEvent.value?.hostIds ?? []);

      _fill(_editingEvent.value!);
    } else {
      // default flow
      initialDateTime = DateTime.now();
      selectedCalendarEventType.value = CalendarEventType.meeting;
    }

    // avatar
    ownerAvatarUrl.value = Constants.avatar() ?? '';
    ownerDisplayName.value = Constants.displayName();

    // allday init
    if (_argumentIsAllDay) {
      calendarController = CalendarDateTimePickerController(
        fillWithInput: true,
        input: CalendarDateTimePickerResult(
          allday: _argumentIsAllDay,
          startDate: initialDateTime,
        ),
      );
      calendarController.addListener(_onCalendarControllerChanged);
    }

    if (showNotSyncNoti) {
      notSyncNotiExpanableController.expanded = true;
    }

    _initAttachmentFilesController();

    _initBsmRepeatActions();

    _saveOriginalRequestFromUI();

    _updateRoomsApprover();

    // Initialize custom event types
    _initCustomEventTypes();

    if (hosts.isEmpty && isEditing.value == false) {
      hosts.add(
        Assignee(
          id: int.parse(Constants.userId()),
          displayName: LocaleKeys.task_assignee_assignMe.tr,
          avatar: Constants.avatar(),
          avatarThumbPattern: Constants.avatar(),
        ),
      );

      selectedHosts.add(hosts.first);
    }
  }

  Future _updateRoomsApprover() async {
    final userInfoApi = UserInfoApi();
    List<int> userIds = [];
    for (var element in rooms) {
      if (element.approverIds?.isNotEmpty == true) {
        userIds.addAll(element.approverIds!.take(roomApproversPreview));
      }
    }
    if (userIds.isNotEmpty) {
      final users = await userInfoApi.getListUser(userIds);

      final listUsers =
          (users.data ?? []).map((e) => GPUser.fromJson(e.toJson())).toList();
      for (var element in rooms) {
        final roomApprovers = listUsers
            .where((e) => element.approverIds?.contains(e.id) == true)
            .toList();
        element.approvers = roomApprovers;
        update(['room_${element.id}']);
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    calendarController.removeListener(_onCalendarControllerChanged);
    _debounce?.cancel();
  }

  @override
  void handleError(Object error, StackTrace s) async {
    Sentry.captureException(
      error,
      stackTrace: s,
    );

    if (error is ForbiddenException) {
      String errorMsg = error.message ?? defaultErrorMsg ?? "";
      if (errorMsg.isNotEmpty) {
        Popup.instance
            .showSnackBar(message: errorMsg, type: SnackbarType.error);
      }
    } else {
      super.handleError(error, s);
    }
  }

  Timer? _debounce;
  void _onCalendarControllerChanged() {
    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      final result = calendarController.value;

      if (result != null && result != calendarController.previousTime) {
        if (allday.value != result.allday) {
          allday.value = result.allday;
          resetRemindAndRepeat();
        }
        // Check phòng họp bận sau khi đổi time
        if (rooms.isNotEmpty) {
          final hasBusyRoom = await _hasBusyRoom();
          if (hasBusyRoom) {
            calendarController.value = calendarController.previousTime;
            return;
          }
        }
        calendarController.previousTime = calendarController.value;
        if (calendarController.isDateTimeUpdated) {
          resetRemindAndRepeat();
        } else {
          calendarController.isDateTimeUpdated = false;
        }
      }

      onchangeDateTime(this);

      saveButtonEnabled.value = !calendarController.hasError;
    });
  }

  Future<bool> _loginGoogle() async {
    return await GoogleSignin()
        .loginGoogle(handleSignInGoogle: _handleSignInGoogle);
  }

  Future<void> _handleSignInGoogle(dynamic mapJson) async {
    if (GoogleSignin().googleResponseCheck(mapJson)) {
      try {
        final result =
            await _calendarService.signInGoogle(data: mapJson, hasSync: false);
        googleEmail = result.data["data"]["email"];
      } catch (e, s) {
        handleError(e, s);
      }
    }
  }

  void onCreateEventPressed() async {
    if (saveButtonEnabled.value == false) return;
    isNextButtonEnabled.value = false;
    saveButtonEnabled.value = false;

    final CreateCalendarEventRequest createCalendarEventRequest =
        _getCreateCalendarRequest();

    try {
      if (rooms.isNotEmpty) {
        saveButtonEnabled.value = false;
        final hasBusyRoom = await _hasBusyRoom();
        if (hasBusyRoom) {
          saveButtonEnabled.value = true;
          return;
        }
      } else {
        createCalendarEventRequest.rooms = null;
      }

      if (!await handleGoogleMeeting(createCalendarEventRequest)) {
        return;
      }

      saveButtonEnabled.value = false;
      late CalendarEventList response;
      response = await _calendarService.createEvent(createCalendarEventRequest);
      saveButtonEnabled.value = true;
      await PlatformNavigator.pop(response.toJson(), result: response.toJson());
    } catch (e, trace) {
      saveButtonEnabled.value = true;
      logDebug(trace);
      handleError(e, trace);
    }

    isNextButtonEnabled.value = true;
  }

  void _setStartEndDateVar() {
    final scheduleData = calendarController.value;
    if (allday.value) {
      startDate = scheduleData?.startDate
              .applyTimeOfDay(hour: 0, minute: 0)
              .millisecondsSinceEpoch ??
          0;
      endDate = scheduleData?.endDate
              ?.applyTimeOfDay(hour: 23, minute: 59)
              .millisecondsSinceEpoch ??
          0;
    } else {
      startDate = scheduleData?.startDate.millisecondsSinceEpoch ?? 0;
      endDate = scheduleData?.endDate?.millisecondsSinceEpoch ?? 0;
    }
  }

  void onSelectRoom() async {
    try {
      final scheduleData = calendarController.value;
      _setStartEndDateVar();
      isLoading.value = true;
      final room =
          await _calendarService.getListRoom(from: startDate, to: endDate);
      isLoading.value = false;

      if (room.isEmpty) {
        Popup.instance.showBottomSheet(WarningDialog(
          showWarningIcon: false,
          title: LocaleKeys.calendar_room_no_room_title.tr,
          content: LocaleKeys.calendar_room_no_room_description.tr,
          buttonColor: GPColor.functionAccentWorkPrimary,
          textButtonColor: GPColor.functionAlwaysLightPrimary,
        ));
        return;
      }

      final hasAvailableRoom = room.any((element) => element.available == true);
      if (!hasAvailableRoom) {
        final startTime = DateFormat('hh:mm a')
            .format(scheduleData?.startDate ?? DateTime.now());
        final endTime = DateFormat('hh:mm a')
            .format(scheduleData?.endDate ?? DateTime.now());
        String title = '';
        if (allday.value) {
          title = LocaleKeys.calendar_room_all_room_busy_all_day_event_title.tr;
        } else {
          title = LocaleKeys.calendar_room_all_room_busy_title.tr
              .replaceFirst('%1', startTime)
              .replaceFirst('%2', endTime);
        }
        Popup.instance.showBottomSheet(WarningDialog(
          title: title,
          content: LocaleKeys.calendar_room_all_room_busy_description.tr,
          buttonColor: GPColor.functionAccentWorkPrimary,
          textButtonColor: GPColor.functionAlwaysLightPrimary,
        ));
      } else {
        final result = await Get.toNamed(CalendarRouterName.calendarRoomSelect,
            arguments: {
              'rooms': room,
              'from_at': startDate,
              'to_at': endDate,
              'selected_rooms': rooms
            });
        if (result is List<EventRoom>) {
          rooms.clear();
          rooms.addAll(result);
          if (rooms.isNotEmpty) {
            selectedRepeatOption.value = CalendarEventRepeatOption.noRepeat;
          }
        }
      }
    } catch (e, trace) {
      isLoading.value = false;
      logDebug(trace);
      handleError(e, trace);
    }
  }

  void onTapRemoveRoom(EventRoom? room) {
    isDeletingRoom = true;
    rooms.remove(room);
  }

  void onBack() async {
    Utils.dismissKeyboard();
    if (isEditing.value) {
      await Popup.instance.showBottomSheet(
        ConfirmDeleteDialog(
          title: LocaleKeys.taskProject_discard_editing.tr,
          content: LocaleKeys.calendar_collab_discard_editing_message.tr,
          cancelButtonTitle: LocaleKeys.calendar_collab_back.tr,
          confirmButtonTitle: LocaleKeys.calendar_collab_discard.tr,
          confirmButtonBgColor: GPColor.functionAccentWorkPrimary,
          confirmButtonTextColor: GPColor.functionAlwaysLightPrimary,
          icon: const SvgWidget(
            'assets/images/svg/discard-editing-meeting-collab.svg',
            width: 64,
            height: 64,
          ),
          onConfirmDelete: () async {
            _closeBottomSheet();
            Utils.back();
          },
        ),
      );
    } else {
      await Popup.instance.showBottomSheet(
        ConfirmDeleteDialog(
          title: LocaleKeys.calendar_create_title_discard_event.tr.format(
              [selectedCalendarEventType.value.displayTitle.toLowerCase()]),
          content: LocaleKeys.calendar_create_message_discard_event.tr
              .format([selectedCalendarEventType.value.displayTitle]),
          cancelButtonTitle:
              LocaleKeys.calendar_create_button_back_discard_event.tr,
          confirmButtonTitle:
              LocaleKeys.calendar_create_button_confirm_discard_event.tr,
          icon: SvgWidget(
            'assets/images/ic24-line15-xmark-circle.png',
            color: GPColor.functionNegativePrimary,
            width: 59,
            height: 59,
          ),
          onConfirmDelete: () async {
            _closeBottomSheet();
            Utils.back();
          },
        ),
      );
    }
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }

  void selectInvitees() async {
    // Mode
    var mode = SelectInviteesOptionsMode.selectNew;

    // Đã từng chọn từ trước thì mode = SelectInviteesOptionsMode.selectNewWithValue
    if (selectedAssignees.isNotEmpty ||
        selectedDepartments.isNotEmpty ||
        selectedRoles.isNotEmpty ||
        selectedThreads.isNotEmpty ||
        ignoreMembers.isNotEmpty) {
      mode = SelectInviteesOptionsMode.selectNewWithValue;
    }

    // edit flow
    if (isEditing.value) {
      mode = SelectInviteesOptionsMode.edit;
    }

    final sAssignees = List<Assignee>.from(selectedAssignees);

    final result = await Get.toNamed(
      RouterName.selectInvitees,
      arguments: SelectInviteesOptions(
        actionButtonTitle: LocaleKeys.calendar_done.tr,
        mode: mode,
        tabs: [
          SelectInviteeTabs.member,
          SelectInviteeTabs.group,
          SelectInviteeTabs.department,
          SelectInviteeTabs.role,
        ],
        selectedMembers: sAssignees,
        selectedDepartments:
            List<OrganizationDepartment>.from(selectedDepartments),
        selectedRoles: List<OrganizationRole>.from(selectedRoles),
        selectedThreads: List<Conversation>.from(selectedThreads),
        ignoreUsers: List<Participant>.from(ignoreMembers),
        selectedMemberIds: selectedAssigneeIds,
        needGetAllSelectedMembers: true,
      ),
    );
    if (result is SelectInviteesResult) {
      selectedAssignees.value = List.from(result.selectedMembers ?? []);
      selectedDepartments.value = List.from(result.selectedDepartments ?? []);
      selectedRoles.value = List.from(result.selectedRoles ?? []);
      selectedThreads.value = List.from(result.selectedThreads ?? []);
      ignoreMembers.value = List.from(result.ignoreParticipants ?? []);
      selectedAssigneeIds.value =
          selectedAssignees.map((element) => element.id).toList();
      hasChangeThread = result.hasChangeThread ?? false;
      hasChangeDepartment = result.hasChangeDepartment ?? false;
      hasChangeRole = result.hasChangeRole ?? false;

      // 05/2025
      final selectedUsers = List<Assignee>.from(result.selectedMembers ?? [])
        ..add(Assignee(id: int.parse(Constants.userId()), displayName: ""));
      final handleDataModel = handleHostSelected(
          hosts: selectedHosts, selectedHosts: selectedUsers);
      if (handleDataModel != null) {
        for (var e in handleDataModel.removeHosts) {
          selectedHosts.remove(e);
          selectedHostIds.remove(e.id);
          removeHostOnEditInvitee.add(e.id);
        }
      }
    }
  }

  void onMeetChanged(bool value) {
    hasMeet.value = value;
    notSyncNotiExpanableController.expanded = showNotSyncNoti;
  }

  void onAIAssistantChanged(bool value) {
    hasAIAssistant.value = value;
  }

  void selectHost() async {
    var mode = SelectInviteesOptionsMode.selectNew;
    if (selectedHosts.isNotEmpty) {
      mode = SelectInviteesOptionsMode.selectNewWithValue;
    }
    try {
      final result = await Get.toNamed(
        RouterName.selectInvitees,
        arguments: SelectInviteesOptions(
          title: LocaleKeys.calendar_create_add_host.tr,
          actionButtonTitle: LocaleKeys.calendar_done.tr,
          mode: mode,
          tabs: [SelectInviteeTabs.member],
          selectInviteesPickMode: SelectInviteesPickMode.pickMultiple,
          selectedMembers: List<Assignee>.from(selectedHosts),
          arguments: {
            "ignore_me": !isEditing.value,
          },
          sConfigs: {
            'need_to_check_press_done': false,
          },
        ),
      );
      if (result is SelectInviteesResult) {
        final sMembers = result.selectedMembers;

        final currentHosts = [];

        if (isEditing.value == false) {
          selectedHosts.value = sMembers ?? [];

          currentHosts.addAll(selectedHosts);
        } else {
          hostDataHandlerModel = handleHostSelected(
            hosts: selectedHosts,
            selectedHosts: result.selectedMembers ?? [],
          );

          if (hostDataHandlerModel != null) {
            selectedHosts.clear();
            selectedHosts.addAll(hostDataHandlerModel?.hosts ?? []);

            selectedHostIds.clear();
            selectedHostIds.addAll(hostDataHandlerModel?.hostIds ?? []);
          }

          currentHosts.addAll(hostDataHandlerModel?.addHosts ?? []);
          logDebug('hostDataHandlerModel -> $hostDataHandlerModel');
        }

        for (var e in selectedHosts) {
          if (!selectedAssignees.contains(e)) {
            // bỏ userMe
            if (e.id == int.parse(Constants.userId())) {
              continue;
            }
            selectedAssignees.add(e);
            selectedAssigneeIds.add(e.id);
          }
        }
      }
    } catch (e, s) {
      handleError(e, s);
    }
  }

  void _fill(CalendarEventList event) {
    titleController.text = event.title ?? '';
    description.value = event.description ?? '';
    //hacnguyen : testing
    // emailGuests.value = [
    //   "<EMAIL>",
    //   "<EMAIL>",
    //   "<EMAIL>",
    //   "<EMAIL>",
    //   "<EMAIL>",
    // ];
    emailGuests.value = event.emails ?? [];

    final startDate = DateTime.fromMillisecondsSinceEpoch(event.startAt ?? 0);

    // remind
    Duration? duration;
    CalendarEventRemindOptionEnum? remindOptionEnum;
    if ((event.schedule?.remindBefore?.length ?? 0) > 0) {
      if (event.schedule?.isAllday ?? false) {
        final optionsAllday = CalendarEventRemindOption.optionsAllday;
        final match = optionsAllday.where((element) =>
            element.duration ==
            event.schedule!.remindBefore!.first.milliseconds);
        if (match.isNotEmpty) {
          final result = match.first;
          duration = result.duration;
          remindOptionEnum = result.optionEnum;
        }
      } else {
        duration = event.schedule!.remindBefore!.first.milliseconds;
      }
    }

    // remind
    selectedRemindOption.value = CalendarEventRemindOption(
      duration: duration,
      optionEnum: remindOptionEnum,
    );

    // repeat
    selectedRepeatOption.value =
        CalendarEventRepeatOption.fromCalendarEventScheduleType(
      event.schedule?.type ?? CalendarEventScheduleType.once,
      event.schedule?.value ?? [],
      startDate,
      event.schedule?.isAllday ?? false,
      event.schedule?.rRule,
    );

    // members
    _getDepartmentsAndRolesAndGroupThenFill(event);

    // GG Meet
    hasMeet.value = event.hasMeeting ?? false;

    final dateTimeInput = CalendarDateTimePickerResult(
      allday: event.schedule?.isAllday ?? _argumentIsAllDay,
      startDate: startDate,
      endDate: DateTime.fromMillisecondsSinceEpoch(event.endAt ?? 0),
    );
    calendarController = CalendarDateTimePickerController(
        fillWithInput: true, input: dateTimeInput, previousTime: dateTimeInput);
    calendarController.addListener(_onCalendarControllerChanged);
  }

  void _getDepartmentsAndRolesAndGroupThenFill(CalendarEventList event) async {
    // members
    selectedAssignees.value = event.attendees
            ?.map(
                (e) => AssigneeExtForCalendar.fromCalendarEventListAttendee(e))
            .toList() ??
        [];
    selectedAssigneeIds.addAll(event.attendeesIds ?? []);

    // departments
    if ((event.toDepartmentIds != null &&
            event.toDepartmentIds?.isNotEmpty == true) ||
        (event.toRoleIds != null && event.toRoleIds?.isNotEmpty == true)) {
      final _service = OrganizationAPI();
      final results =
          await Future.wait([_service.getDepartments(), _service.getRoles()]);
      final deptsResponse = results[0] as OrganizationDepartmentResponse;
      _tempDeptsFlat = [];
      // Department đầu tiên chứa tất cả dept của ws
      final workspaceDept = deptsResponse.data[0];
      final deps =
          List<OrganizationDepartment>.from(_flatDeptOne(workspaceDept));
      final selected = deps
          .where(
              (element) => event.toDepartmentIds?.contains(element.id) ?? false)
          .toList();
      selectedDepartments.value = selected;

      // roles
      final listRoles = results[1] as List<OrganizationRole>;
      selectedRoles.value = listRoles
          .where((element) => event.toRoleIds?.contains(element.id) ?? false)
          .toList();
    }

    // groups
    final toThreadIds = event.toThreadIds;
    if (toThreadIds != null && toThreadIds.isNotEmpty) {
      final _chatService = ChatAPI();
      final getThreadApis = toThreadIds.map((e) async {
        try {
          return await _chatService.getConversation(id: int.tryParse(e) ?? 0);
        } catch (error) {
          return Conversation(id: int.tryParse(e) ?? 0);
        }
      }).toList();
      final threads = await Future.wait(getThreadApis);
      selectedThreads.addAll(threads);
    }

    // ignore participants
    final ignoreUserIds = event.ignoreUserIds;
    if (ignoreUserIds != null && ignoreUserIds.isNotEmpty) {
      ignoreMembers
          .addAll(ignoreUserIds.map((e) => Participant(id: e.toString())));
    }
  }

  /// to execute recursive algorithm
  List<OrganizationDepartment> _tempDeptsFlat = [];
  List<OrganizationDepartment> _flatDeptOne(
      OrganizationDepartment organizationDepartment) {
    _tempDeptsFlat.add(organizationDepartment);
    for (var item in organizationDepartment.children) {
      _flatDeptOne(item);
    }
    return _tempDeptsFlat;
  }

  CreateCalendarEventRequest _getCreateCalendarRequest() {
    final scheduleData = calendarController.value;

    List scheduleValue = [];
    if (selectedRepeatOption.value.rRule != "") {
      // update lại schedule value lặp ngày theo rRule
      scheduleValue.addAll(
          selectedRepeatOption.value.rRuleModel?.getScheduleValue() ?? []);
    } else {
      switch (selectedRepeatOption.value.optionEnum) {
        case CalendarEventRepeatOptionEnum.weekly:
        case CalendarEventRepeatOptionEnum.weeklyAllDay:
          scheduleValue = [scheduleData?.startDate.weekdayJS ?? 0];
          break;
        case CalendarEventRepeatOptionEnum.monthly:
        case CalendarEventRepeatOptionEnum.monthlyAllDay:
          scheduleValue = [scheduleData?.startDate.day ?? 0];
          break;
        case CalendarEventRepeatOptionEnum.weeklyAllDayMondayToFriday:
        case CalendarEventRepeatOptionEnum.weeklyMondayToFriday:
          scheduleValue = [1, 2, 3, 4, 5];
          break;
        default:
          // Nếu user k chọn giờ thì mặc định selected value = 0h00 UTC + 0
          // Nên cần convert qua cùng locale
          final startDate = scheduleData?.startDate;
          if (startDate != null && startDate.isUtc) {
            // constructor DateTime() sẽ ra default locale
            final value = DateTime(startDate.year, startDate.month,
                startDate.day, startDate.hour, startDate.minute);
            scheduleValue = [value.millisecondsSinceEpoch];
          } else {
            scheduleValue = [
              scheduleData?.startDate.millisecondsSinceEpoch ?? 0
            ];
          }
          break;
      }
    }

    // case nhiều ngày
    final startDate = scheduleData?.startDate;
    var endTime = scheduleData?.endDate;

    // Todo(Task)/Reminder nếu k phải cả ngày mặc định kéo dài 30p
    // Nên fix vào request body lúc tạo và edit là 30p luôn
    if (selectedCalendarEventType.value == CalendarEventType.reminder ||
        selectedCalendarEventType.value == CalendarEventType.task) {
      final aFewMinutesLater = startDate?.add(const Duration(minutes: 30));
      endTime = aFewMinutesLater;
      scheduleData?.endDate = aFewMinutesLater;
    }

    var duration = 0;
    if (startDate != null && endTime != null) {
      final startDateDMY =
          DateTime(startDate.year, startDate.month, startDate.day);
      final endDateDMY = DateTime(endTime.year, endTime.month, endTime.day);
      if (endDateDMY.isAfter(startDateDMY)) {
        duration = endDateDMY.difference(startDateDMY).inDays;
      }
    }

    // Meeting
    var hasMeeting = hasMeet.value;

    // Mời tham gia
    var attendeesId = selectedAssignees.map((e) => e.id).toList();
    var departmentIds =
        selectedDepartments.map((e) => e.id).whereNotNull().toList();
    var roleIds = selectedRoles.map((e) => e.id ?? '').toList();
    var threadIds = selectedThreads.map((e) => e.id.toString()).toList();
    var ignoreMemberIds = ignoreMembers
        .map((element) => int.tryParse(element.id ?? ''))
        .whereNotNull()
        .toList();

    var hostIds = selectedHosts.map((e) => e.id).toList();
    var addedHostIds = <int>[];
    var removedHostIds = <int>[];

    // case edit, và có thêm/xóa bớt host
    if (hostDataHandlerModel != null) {
      addedHostIds.addAll(hostDataHandlerModel?.addHostIds ?? []);
      removedHostIds.addAll(hostDataHandlerModel?.removeHostIds ?? []);
    } else {
      // case edit xoá người tham gia là host
      if (removeHostOnEditInvitee.isNotEmpty) {
        removedHostIds.addAll(removeHostOnEditInvitee);
      }
    }

    final editingEvent = _editingEvent.value;

    // Get different departments
    final selectedDepartmentIds = departmentIds.toSet();
    final addedDepartments = editingEvent?.toDepartmentIds?.toSet();
    final diffDepartmentsRemove =
        addedDepartments?.difference(selectedDepartmentIds).toList() ?? [];

    // Get different roles
    final selectedRoleIds = roleIds.toSet();
    final addedRoles = editingEvent?.toRoleIds?.toSet();
    final diffRolesRemove =
        addedRoles?.difference(selectedRoleIds).toList() ?? [];

    // Get different threads
    final selectedThreadIds = threadIds.toSet();
    final addedThreads = editingEvent?.toThreadIds?.toSet();
    final diffThreadsRemove =
        addedThreads?.difference(selectedThreadIds).toList() ?? [];

    // all day
    var isAllday = scheduleData?.allday;

    // description
    String? _description = description.value;

    if ((editingEvent?.description ?? '') == description.value) {
      _description = null;
    }

    // Remind before
    List<int> remindBefore = [];
    if (selectedRemindOption.value.duration?.inMilliseconds != null) {
      remindBefore = [selectedRemindOption.value.duration!.inMilliseconds];
    }

    // Correct lại các thông tin cho các event type khác nhau
    // Todo/Task sẽ k có meet và mời người khác tham gia
    // Reminder giống Todo/Task và k có allday, description và remind before
    if (selectedCalendarEventType.value == CalendarEventType.task ||
        selectedCalendarEventType.value == CalendarEventType.reminder) {
      hasMeeting = false;
      attendeesId = [];
      departmentIds = [];
      roleIds = [];
      threadIds = [];
      ignoreMemberIds = [];

      hostIds = [];
      addedHostIds = [];
      removedHostIds = [];
    }

    if (selectedCalendarEventType.value == CalendarEventType.reminder) {
      isAllday = false;
      _description = null;
    }

    // Timestamp của ngày bắt đầu event. Muốn tạo event trong quá khứ hoặc
    // tương lai thì truyền from_date lên.
    // Không truyền mặc định sẽ tính từ thời điểm hiện tại.
    final fromDate = scheduleData?.startDate;

    int? endDate;
    if (isEditing.value) {
      endDate = _editingEvent.value?.schedule?.endDate;
    }
    // Nếu có phòng họp không lặp lại
    if (rooms.isNotEmpty) {
      selectedRepeatOption.value = CalendarEventRepeatOption.noRepeat;
    }

    final scheduleRequest = CreateCalendarEventScheduleRequest(
      type: selectedRepeatOption.value.getCESTByRRule(),
      isAllday: isAllday,
      startHour: scheduleData?.startDate.hour,
      startMinute: scheduleData?.startDate.minute,
      endHour: scheduleData?.endDate?.hour,
      endMinute: scheduleData?.endDate?.minute,
      remindBefore: remindBefore,
      value: scheduleValue,
      duration: duration,
      fromDate: fromDate?.millisecondsSinceEpoch,
      endDate: selectedRepeatOption.value.rRule.isNotEmpty ? null : endDate,
      offset: fromDate?.timeZoneOffset.inMinutes,
      rRule: selectedRepeatOption.value.rRule,
      // tz: fromDate?.timeZoneOffset == null
      //     ? null
      //     : Utils.mapTimeZoneOffsetToName(fromDate!.timeZoneOffset),
    );

    // if (hostIds.isNotEmpty) {
    //   attendeesId.addAll(hostIds);
    // }

    return DefaultCreateCalendarEventRequest(
      collabId: collabId,
      type: selectedCalendarEventType.value,
      title: titleController.text,
      description: _description,
      schedule: scheduleRequest,
      attendeesId: attendeesId.toSet().toList(),
      hasMeeting: hasMeeting,
      departmentIds: departmentIds,
      roleIds: roleIds,
      threadIds: threadIds,
      ignoreMemberIds: ignoreMemberIds,
      attachmentFiles: attachmentFilesController.getUploadResponses(),
      emails: emailGuests,
      descriptionRtf: _description != null ? descriptionRtf : null,
      rooms: rooms.toList(),
      ignoreDepartmentIds: diffDepartmentsRemove,
      ignoreRoleIds: diffRolesRemove,
      ignoreThreadIds: diffThreadsRemove,
      customEventType:
          selectedCalendarEventType.value == CalendarEventType.meeting
              ? selectedCustomEventType.value
              : null,
      // case edit, và có thêm/xóa bớt host => không cần truyền host
      hostIds: hostDataHandlerModel != null ? null : hostIds.toSet().toList(),
      addHostIds: addedHostIds.toSet().toList(),
      removeHostIds: removedHostIds.toSet().toList(),
    );
  }

  /*
    Function trước hơi big, nên tách lại,
    tên function `handleGoogleMeeting` hơi không rõ nghĩa, và xử lý khá nhiều action.
  */
  Future<bool> handleGoogleMeeting(
      CreateCalendarEventRequest createCalendarEventRequest) async {
    // Nếu tạo meet và đã đăng nhập, kiểm tra đã có quyền chưa
    if ((createCalendarEventRequest.hasMeeting ?? false) &&
        googleEmail.isNotEmpty) {
      final canCreateMeet = await _checkGoogleScope();
      if (!canCreateMeet) {
        final result = await Popup.instance
            .showBottomSheet(PopupGoogleScope(onConfirm: () async {
          Get.back(result: true);

          return false;
        }));
        if (result ?? false) {
          // user bấm đã hiểu hiện lại login
          final isLoginSuccess = await _loginGoogle();
          if (!isLoginSuccess) {
            saveButtonEnabled.value = true;
            return false;
          }
        } else {
          // user bấm huỷ
          saveButtonEnabled.value = true;
          return false;
        }
      }
    }
    // Nếu tạo meet mà chưa đăng nhập Google, hiển thị đăng nhập Google
    if ((createCalendarEventRequest.hasMeeting ?? false) &&
        googleEmail.isEmpty) {
      final isLoginSuccess = await _loginGoogle();
      if (!isLoginSuccess) {
        saveButtonEnabled.value = true;
        return false;
      }
    }

    return true;
  }

  // ---------- editMethod --------- \\
  void onAppBarSave() async {
    if (saveButtonEnabled.value == false) return;
    final id = _editingEvent.value?.id;
    if (id == null || id.isEmpty) return;

    final eventRequest = _getCreateCalendarRequest();

    if (!await handleGoogleMeeting(eventRequest)) {
      return;
    }

    if (!_hasDiffCalendarEventList(eventRequest, _editingEvent.value!)) {
      await PlatformNavigator.pop(null);
      return;
    }

    saveButtonEnabled.value = false;

    if (rooms.isNotEmpty) {
      saveButtonEnabled.value = false;
      final hasBusyRoom = await _hasBusyRoom();
      if (hasBusyRoom) {
        saveButtonEnabled.value = true;
        return;
      }
    } else {
      if (isDeletingRoom == false) eventRequest.rooms = null;
    }

    final scheduleTypeIsSame =
        eventRequest.schedule?.type == _editingEvent.value?.schedule?.type;

    final bool isCustomize = selectedRepeatOption.value.optionEnum ==
        CalendarEventRepeatOptionEnum.custom;

    // Function eq = const ListEquality().equals;
    // bool scheduleValueIsSame =
    //     eq(eventRequest.schedule?.value, _editingEvent.value?.schedule?.value);
    // // nếu mà event lặp daily thì k cần compare schedule.value
    // if (scheduleTypeIsSame &&
    //     eventRequest.schedule?.type == CalendarEventScheduleType.daily) {
    //   scheduleValueIsSame = true;
    // }
    EditCalendarEventRequestOption? _editOption;
    if (rooms.isEmpty) {
      _editOption = await _chooseEditMethod(isCustomize || scheduleTypeIsSame);
    } else {
      // Nếu edit event lặp và chọn room sau khi ấn save sẽ chọn luôn chỉ sửa sự kiện này
      if (_showChooseEditMethod) {
        _editOption = EditCalendarEventRequestOption(
          EditCalendarEventMethod.individuals,
          identities: [_editingEvent.value?.identity ?? ''],
        );
      } else {
        _editOption = EditCalendarEventRequestOption(
          EditCalendarEventMethod.root,
        );
      }
    }
    if (_editOption == null) {
      saveButtonEnabled.value = true;
      return;
    } else {
      // close popup
      if (_showChooseEditMethod) {
        // Get.back();
        // close popup đã có sẵn ở trong action rùi nên k cần gọi lại nữa.
      }
    }
    // cập nhật lại datetime theo rootEvent, nếu áp dụng cho toàn bộ event
    DateTime eventFromDate = DateTime.fromMillisecondsSinceEpoch(
        eventRequest.schedule?.fromDate ?? 0);

    // convert fromDate về đầu ngày để so sánh đúng
    eventRequest.schedule?.fromDate =
        eventFromDate.applyTimeOfDay(hour: 0, minute: 0).millisecondsSinceEpoch;

    final editingEventFromDate =
        DateTime.fromMillisecondsSinceEpoch(_editingEvent.value?.startAt ?? 0)
            .applyTimeOfDay(hour: 0, minute: 0);

    _editingEvent.value?.schedule?.fromDate =
        editingEventFromDate.millisecondsSinceEpoch;

    if (_showChooseEditMethod) {
      if (_editOption.method == EditCalendarEventMethod.root ||
          _editOption.method == EditCalendarEventMethod.breaK) {
        DateTime rootEventStartAt = DateTime.fromMillisecondsSinceEpoch(
            _editingEvent.value?.startAt ?? 0);
        if (rootEventStartAt.isSameDate(eventFromDate)) {
          // nếu user chọn Tất cả các sự kiện/từ sự kiện này trở đi, không thay đổi ngày, ko gửi fromDate nữa
          eventRequest.schedule?.fromDate = null;
          _editingEvent.value?.schedule?.fromDate = null;
        }
      }
    }

    // Thêm UNTIL để so sánh trong trường hợp lặp sau n ngày
    bool isSameRrule = false;
    if (_editingEvent.value?.schedule?.rRule?.contains('COUNT') ?? false) {
      final indexUntilStr =
          _editingEvent.value?.schedule?.rRule?.indexOf('UNTIL');
      final rrule = eventRequest.schedule?.rRule;
      if (indexUntilStr != null && rrule != null) {
        final untilStr =
            _editingEvent.value?.schedule?.rRule?.substring(indexUntilStr) ??
                "";
        final newRrule = "$rrule$untilStr";
        if (newRrule == _editingEvent.value?.schedule?.rRule) {
          isSameRrule = true;
        }
      }
    }

    // Trường hợp event đc accept/deny là event lặp có ngày kết thúc
    // (lặp sau n ngày/kết thúc vào ngày m) exception event rrule có mỗi UNTIL
    if (_editingEvent.value?.schedule?.rRule?.indexOf('UNTIL') == 0) {
      final rrule = eventRequest.schedule?.rRule;
      if (rrule != null) {
        final newRrule = "$rrule${_editingEvent.value?.schedule?.rRule}";
        if (newRrule == _editingEvent.value?.schedule?.rRule) {
          eventRequest.schedule?.endDate = null;
          isSameRrule = true;
        }
      }
    }

    if (isSameRrule) {
      eventRequest.schedule?.rRule = _editingEvent.value?.schedule?.rRule;
    }

    if (_editingEvent.value?.schedule?.rRule?.isNotEmpty ?? false) {
      _editingEvent.value?.schedule?.endDate = null;
    }

    if (_editingEvent.value?.schedule?.remindNote?.isEmpty ?? true) {
      _editingEvent.value?.schedule?.remindNote = null;
    }

    if (_editingEvent.value?.schedule?.remindBefore == null) {
      _editingEvent.value?.schedule?.remindBefore = [];
    }

    /// các trường không cần thiết phải gửi
    final notChangeFields = getFieldsNotChange(eventRequest);

    // edit không đổi đc type
    eventRequest.type = null;
    eventRequest.hostIds = null;

    eventRequest.editOption = _showChooseEditMethod ? _editOption : null;

    updateAttendees(eventRequest, _editingEvent.value!);
    updateDepartments(eventRequest, _editingEvent.value!);
    updateThreads(eventRequest, _editingEvent.value!);
    updateRoles(eventRequest, _editingEvent.value!);

    try {
      saveButtonEnabled.value = false;
      CalendarEventList? editedEvent;
      late CreateCalendarResult navigationResult;
      editedEvent = await _calendarService.editEvent(id, eventRequest,
          notChangeFields: notChangeFields);
      navigationResult = CreateCalendarResult(
        resultType:
            CreateCalendarResultEnum.edittedEventWithResultIsEdittedEvent,
        result: editedEvent,
        googleEmail: googleEmail,
      );
      if (Get.isRegistered<CalendarListController>()) {
        Get.find<CalendarListController>().delayGetListItems();
      }
      if (Get.isRegistered<CollabCalendarListController>()) {
        Get.find<CollabCalendarListController>().delayGetListItems();
      }
      saveButtonEnabled.value = true;
      Utils.back(result: navigationResult);
    } catch (e, trace) {
      saveButtonEnabled.value = true;
      logDebug(trace);
      handleError(e, trace);
    }
  }

  List<String> getFieldsNotChange(CreateCalendarEventRequest eventRequest) {
    final List<String> fieldsNotChange = [];

    if (eventRequest.schedule == _editingEvent.value?.schedule) {
      fieldsNotChange.add("schedule");
    }

    final eventAttachmentIds =
        eventRequest.attachmentFiles?.map((e) => e["id"]).toList();
    final editingEventAttachmentIds =
        _editingEvent.value?.attachments?.map((e) => e["id"]).toList();

    if (listEquals(eventAttachmentIds, editingEventAttachmentIds)) {
      fieldsNotChange.add("attachments");
    }

    if (eventRequest.hasMeeting == _editingEvent.value?.hasMeeting) {
      fieldsNotChange.add("has_meeting");
    }

    if (eventRequest.customEventType == _editingEvent.value?.meetingType) {
      fieldsNotChange.add("meeting_type_id");
    }

    if (eventRequest.title == _editingEvent.value?.title) {
      fieldsNotChange.add("title");
    }

    if (listEquals(eventRequest.rooms, _editingEvent.value?.rooms)) {
      fieldsNotChange.add("room_ids");
    }

    if (listEquals(eventRequest.emails, (_editingEvent.value?.emails ?? []))) {
      fieldsNotChange.add("attendees_email");
    }

    return fieldsNotChange;
  }

  // to_attendees_id: thì sẽ thêm thằng này vào attendees_id
  // attendees_id: thì sẽ replace thằng trong db bằng thằng này
  void updateAttendees(
    CreateCalendarEventRequest createCalendarEventRequest,
    CalendarEventList? calendarEventList,
  ) {
    final editedAttendees = createCalendarEventRequest.attendeesId?.toSet();
    final attendees = calendarEventList?.attendeesIds?.toSet();

    final diffAttendeesAdd =
        editedAttendees?.difference(attendees ?? {}).toList() ?? [];
    final diffAttendeesRemove =
        attendees?.difference(editedAttendees ?? {}).toList() ?? [];
    createCalendarEventRequest.attendeesId = null;
    createCalendarEventRequest.toAttendeesId =
        diffAttendeesAdd.isNotEmpty ? diffAttendeesAdd : null;
    createCalendarEventRequest.removeAttendeesId =
        diffAttendeesRemove.isNotEmpty ? diffAttendeesRemove : null;

    // 05/2025
    // createCalendarEventRequest.toAttendeesId
    //     ?.addAll(createCalendarEventRequest.addHostIds ?? []);
    // createCalendarEventRequest.removeHostIds?.addAll(diffAttendeesRemove);
  }

  void updateDepartments(
    CreateCalendarEventRequest createCalendarEventRequest,
    CalendarEventList? calendarEventList,
  ) {
    final editedDepartments = createCalendarEventRequest.departmentIds?.toSet();
    final departments = calendarEventList?.toDepartmentIds?.toSet();

    final diffDepartmentsAdd =
        editedDepartments?.difference(departments ?? {}).toList() ?? [];
    final diffDepartmentsRemove =
        departments?.difference(editedDepartments ?? {}).toList() ?? [];
    createCalendarEventRequest.departmentIds = null;
    createCalendarEventRequest.additionalDepartmentIds =
        diffDepartmentsAdd.isNotEmpty ? diffDepartmentsAdd : null;
    createCalendarEventRequest.ignoreDepartmentIds =
        diffDepartmentsRemove.isNotEmpty ? diffDepartmentsRemove : null;
  }

  void updateRoles(
    CreateCalendarEventRequest createCalendarEventRequest,
    CalendarEventList? calendarEventList,
  ) {
    final editedRoles = createCalendarEventRequest.roleIds?.toSet();
    final roles = calendarEventList?.toRoleIds?.toSet();

    final diffRolesAdd = editedRoles?.difference(roles ?? {}).toList() ?? [];
    final diffRolesRemove = roles?.difference(editedRoles ?? {}).toList() ?? [];
    createCalendarEventRequest.roleIds = null;
    createCalendarEventRequest.additionalRoleIds =
        diffRolesAdd.isNotEmpty ? diffRolesAdd : null;
    createCalendarEventRequest.ignoreRoleIds =
        diffRolesRemove.isNotEmpty ? diffRolesRemove : null;
  }

  void updateThreads(
    CreateCalendarEventRequest createCalendarEventRequest,
    CalendarEventList? calendarEventList,
  ) {
    final editedThreads = createCalendarEventRequest.threadIds?.toSet();
    final threads = calendarEventList?.toThreadIds?.toSet();

    final diffThreadsAdd =
        editedThreads?.difference(threads ?? {}).toList() ?? [];
    final diffThreadsRemove =
        threads?.difference(editedThreads ?? {}).toList() ?? [];
    createCalendarEventRequest.threadIds = null;
    createCalendarEventRequest.additionalThreadIds =
        diffThreadsAdd.isNotEmpty ? diffThreadsAdd : null;
    createCalendarEventRequest.ignoreThreadIds =
        diffThreadsRemove.isNotEmpty ? diffThreadsRemove : null;
  }

  Future<bool> _hasBusyRoom() async {
    _setStartEndDateVar();
    final allRoom = await _calendarService.getListRoom(
        from: startDate, to: endDate, ignoreEventId: _editingEvent.value?.id);
    final bookedRoomIds = rooms.map((e) => e.id).toList();
    final bookedRooms =
        allRoom.where((element) => bookedRoomIds.contains(element.id));
    final hasBusyRoom =
        bookedRooms.any((element) => element.available == false);
    if (hasBusyRoom) {
      final scheduleData = calendarController.value;
      final startTime = DateFormat('hh:mm a')
          .format(scheduleData?.startDate ?? DateTime.now());
      final endTime =
          DateFormat('hh:mm a').format(scheduleData?.endDate ?? DateTime.now());
      final title = LocaleKeys.calendar_room_edit_time_room_busy_title.tr
          .replaceFirst('%1', startTime)
          .replaceFirst('%2', endTime);
      await Popup.instance.showBottomSheet(WarningDialog(
        title: title,
        content: LocaleKeys.calendar_room_edit_time_room_busy_description.tr,
        buttonColor: GPColor.functionAccentWorkPrimary,
        textButtonColor: GPColor.functionAlwaysLightPrimary,
      ));
      return true;
    }
    return false;
  }

  Future<EditCalendarEventRequestOption?> _chooseEditMethod(
      bool hasChangeScheduleType) async {
    // edit options
    EditCalendarEventRequestOption editOption =
        EditCalendarEventRequestOption(EditCalendarEventMethod.root);

    // chỉ hiển thị popup Thay đổi sự kiện nếu event được sửa lặp theo ngày/tuần/tháng
    if (_showChooseEditMethod) {
      final method = await _showBottomSheetChooseEditMethod(
        _editingEvent.value?.title ?? '',
        hasChangeScheduleType,
      );
      if (method is EditCalendarEventMethod) {
        editOption = EditCalendarEventRequestOption(
          method,
          identities: [_editingEvent.value?.identity ?? ''],
        );
      } else {
        // cancel
        return null;
      }
    }

    return editOption;
  }

  Future<EditCalendarEventMethod?> _showBottomSheetChooseEditMethod(
    String eventName,
    bool hasChangeScheduleType,
  ) async {
    final result = await Popup.instance.showBottomSheet(
      CalendarChooseEditMethodPopup(
          calendarEventType: selectedCalendarEventType.value,
          isDeleteMode: false,
          hasChangeScheduleType: hasChangeScheduleType),
    );
    return result;
  }

  void resetRemindAndRepeat() {
    selectedRemindOption.value = (allday.value
        ? CalendarEventRemindOption.optionsAllday.last
        : CalendarEventRemindOption.options[2]);

    // Repeat
    final oldOpts = (allday.value
        ? CalendarEventRepeatOption.options
        : CalendarEventRepeatOption.optionsAllday);
    final newOpts = (allday.value
        ? CalendarEventRepeatOption.optionsAllday
        : CalendarEventRepeatOption.options);

    final index = oldOpts.indexOf(selectedRepeatOption.value.optionEnum);
    if (index != -1) {
      final newSelectedOpt = CalendarEventRepeatOption(
        dateTime: selectedRepeatOption.value.dateTime,
        optionEnum:
            newOpts[oldOpts.indexOf(selectedRepeatOption.value.optionEnum)],
        rRuleStr: selectedRepeatOption.value.rRule,
      );
      selectedRepeatOption.value = newSelectedOpt;
    }
  }

  void onSelectRemindOption() async {
    List<BottomSheetModel> actions = (allday.value
            ? CalendarEventRemindOption.optionsAllday
            : CalendarEventRemindOption.options)
        .map((e) => BottomSheetModel(
            value: e,
            displayName: e.displayText,
            iconAsset: null,
            isSelected: e.duration == selectedRemindOption.value.duration))
        .toList();
    final result = await Popup.instance
        .showBottomSheet(BottomSheetSelectionWidget(data: actions));
    if (result is CalendarEventRemindOption) {
      selectedRemindOption.value = result;
    }
  }

  void onSelectRepeatOption() async {
    final result = await Popup.instance
        .showBottomSheet(BottomSheetSelectionWidget(data: bsmRepeats));
    if (result is CalendarEventRepeatOption) {
      CalendarEventRepeatOption cero = result;

      if (cero.optionEnum == CalendarEventRepeatOptionEnum.custom) {
        if (bsmRepeats.first.value.rRule != cero.rRule) {
          openCustomizeRRulePage(this);
        }
      } else {
        selectedRepeatOption.value = result;
        removeCustomBsm(this);
      }

      logDebug("ToanNM rRule: ${selectedRepeatOption.value.rRule}");
    }
  }

  void onEditDescription() async {
    final input = CalendarDescriptionInput(
        description: description.value,
        title: selectedCalendarEventType.value.descriptionDisplayText);
    final _description = await Get.toNamed(
        CalendarRouterName.calendarDescription,
        arguments: input);
    if (_description is Map) {
      description.value = _description['description'];
      descriptionRtf = _description['description_rtf'];
    }
  }

  void onTypePickerChanged(CalendarEventType selectedType) {
    selectedCalendarEventType.value = selectedType;
  }

  void onCustomEventTypeSelected(CalendarCustomEventType result) {
    if (selectedCustomEventType.value?.id == result.id) {
      selectedCustomEventType.value = null;
    } else {
      selectedCustomEventType.value = result;
    }
  }

  void _initCustomEventTypes() async {
    // Initialize with empty list, will be populated when needed
    customEventTypes.value = [];
    // Fetch event types from API
    await fetchEventTypes();
  }

  Future<void> showEventTypeBottomSheet() async {
    // Create bottom sheet models from event types
    final actions = customEventTypes.map((type) {
      return BottomSheetModel(
        value: type,
        displayName: type.name,
        isSelected: selectedCustomEventType.value != null &&
            type.id == selectedCustomEventType.value!.id,
        leadingIcon: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: type.color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        crossAxisAlignment: CrossAxisAlignment.center,
      );
    }).toList();

    // Show bottom sheet
    final result = await Popup.instance.showBottomSheet(
      BottomSheetSelectionWidget(
        title: LocaleKeys.calendar_create_event_type_title.tr,
        data: actions,
        emptyWidget: Padding(
          padding: const EdgeInsets.all(40),
          child: Column(
            children: [
              SvgWidget('assets/images/svg/img-empty-data.svg'),
              const SizedBox(height: 12),
              Text(
                LocaleKeys.calendar_create_event_type_empty.tr,
                style: textStyle(GPTypography.bodyLarge)
                    ?.mergeColor(GPColor.contentSecondary),
              ),
            ],
          ),
        ),
      ),
    );

    // Handle selection
    if (result != null) {
      onCustomEventTypeSelected(result);
    }
  }

  Future<void> fetchEventTypes() async {
    try {
      isLoading.value = true;
      final response = await _calendarService.getEventTypes();
      customEventTypes.value = response.eventTypes;

      if (_editingEvent.value?.hasMeetingTypeId ?? false) {
        selectedCustomEventType.value = customEventTypes.firstWhereOrNull(
          (e) => e.id == _editingEvent.value?.meetingTypeId,
        );
      }
    } catch (e, s) {
      handleError(e, s);
    } finally {
      isLoading.value = false;
    }
  }

  // MARK: - Attachment files

  void _initAttachmentFilesController() {
    attachmentFilesController = CalendarAttachmentFilesController(
      downloadSource: DownloadSource(
        id: _editingEvent.value?.id ?? '',
        type: DownloadSourceIdType.calendar,
      ),
      initialValue: _editingEvent.value?.attachments ?? [],
      onUploading: () {
        saveButtonEnabled.value = false;
      },
      onUploadDone: () {
        saveButtonEnabled.value = true;
      },
    );
  }

  void _initBsmRepeatActions() {
    // options lặp
    List<CalendarEventRepeatOption> repeatOptions = (allday.value
            ? CalendarEventRepeatOption.optionsAllday
            : CalendarEventRepeatOption.options)
        .map((e) => CalendarEventRepeatOption(
              dateTime: calendarController.value?.startDate ?? initialDateTime,
              optionEnum: e,
            ))
        .toList();

    bsmRepeats.addAll(getCustomRRuleActions(repeatOptions));

    if (selectedRepeatOption.value.rRule.isNotEmpty) {
      updateBsmSelected(this, selectedRepeatOption.value.rRule);
    }
  }

  // CreateCalendarEventRequest? _originCreateCalendarEventRequest;
  void _saveOriginalRequestFromUI() async {
    await Future.delayed(500.milliseconds);
    // _originCreateCalendarEventRequest =
    _getCreateCalendarRequest();
  }

  Future<bool> _checkGoogleScope() async {
    final ggScopeChecking = await _calendarService.googleScopeChecking();
    return CheckScopeGoogleCalendar.hasFullCalendarScope(
        List<String>.from(ggScopeChecking["scopes"]));
  }

  Future hideTooltip() async {
    if (tooltipController.isVisible) {
      await tooltipController.hideTooltip();
    }
  }
}

extension WeekdayJS on DateTime {
  /// to sync with BE logic that use JS, the value is [0...6] according to [sunday...saturday]
  int get weekdayJS {
    if (weekday == DateTime.sunday) {
      return 0;
    }
    return weekday;
  }
}

mixin _CompareCreateRequestComparation {
  bool _hasDiffCalendarEventList(
    CreateCalendarEventRequest createCalendarEventRequest,
    CalendarEventList? calendarEventList,
  ) {
    /*
      Compare các thay đổi hiện tại
        - Khi thêm mới: hiển thị dialog confirm user trước khi back
        - Khi sửa:      chỉ call api update nếu có sự thay đổi
    */
    final _rSchedule = createCalendarEventRequest.schedule;

    if (_rSchedule == null) return false;

    if (calendarEventList == null || calendarEventList.schedule == null) {
      // trường hợp tạo mới
      return (createCalendarEventRequest.title != null &&
              createCalendarEventRequest.title!.isNotEmpty) ||
          (createCalendarEventRequest.description != null &&
              createCalendarEventRequest.description!.isNotEmpty) ||
          createCalendarEventRequest.schedule?.type !=
              CalendarEventScheduleType.once ||
          createCalendarEventRequest.schedule?.isAllday != false ||
          (createCalendarEventRequest.departmentIds?.isNotEmpty ?? false) ||
          (createCalendarEventRequest.roleIds?.isNotEmpty ?? false);
    }

    // trường hợp sửa
    Function eq = const ListEquality().equals;

    final _cSchedule = calendarEventList.schedule!;

    return createCalendarEventRequest.type != calendarEventList.type ||
        _rSchedule.type != _cSchedule.type ||
        _rSchedule.duration != _cSchedule.duration ||
        _rSchedule.isAllday != _cSchedule.isAllday ||
        !eq(
          _rSchedule.value,
          _cSchedule.value,
        ) ||
        !eq(
          _rSchedule.remindBefore,
          _cSchedule.remindBefore,
        ) ||
        createCalendarEventRequest.title != calendarEventList.title ||
        createCalendarEventRequest.description !=
            calendarEventList.description ||
        !eq(
          createCalendarEventRequest.attendeesId ?? [],
          calendarEventList.attendeesIds ?? [],
        ) ||
        !eq(
          createCalendarEventRequest.roleIds ?? [],
          calendarEventList.toRoleIds ?? [],
        ) ||
        !eq(
          createCalendarEventRequest.departmentIds ?? [],
          calendarEventList.toDepartmentIds ?? [],
        ) ||
        !eq(
          createCalendarEventRequest.threadIds ?? [],
          calendarEventList.toThreadIds ?? [],
        ) ||
        !eq(
          createCalendarEventRequest.ignoreMemberIds ?? [],
          calendarEventList.ignoreUserIds ?? [],
        ) ||
        !eq(
          createCalendarEventRequest.attachmentFiles ?? [],
          calendarEventList.attachments ?? [],
        ) ||
        createCalendarEventRequest.hasMeeting != calendarEventList.hasMeeting ||
        _rSchedule.fromDate != calendarEventList.startAt ||
        _rSchedule.endDate != calendarEventList.endAt;
  }
}

mixin _CustomizeRecurrenceRule {
  List<BottomSheetModel> getCustomRRuleActions(
      List<CalendarEventRepeatOption> inputs) {
    List<BottomSheetModel> actions = [];

    actions = inputs
        .map((e) => BottomSheetModel(
            displayName: e.displayName, iconAsset: null, value: e))
        .toList();

    return actions;
  }

  Future<CalendarEventRepeatOption?> openCustomizeRRulePage(
      CreateCalendarEventController controller) async {
    controller.bsmRepeats.last.isSelected = false;

    _checkSeletedBsm(controller);

    var results = await Get.toNamed(
      CalendarRouterName.calendarRepeatCustomize,
      arguments: {
        "startDate": controller.calendarController.value?.startDate,
        "listActions": controller.bsmRepeats,
        "rRule": controller.selectedRepeatOption.value.rRule,
      },
    );

    if (results != null && results["rRule"] != null) {
      controller.selectedRepeatOption.value = CalendarEventRepeatOption(
        optionEnum: CalendarEventRepeatOptionEnum.custom,
        dateTime:
            controller.calendarController.value?.startDate ?? DateTime.now(),
        rRuleStr: results["rRule"],
      );

      updateBsmSelected(controller, results["rRule"]);
    }

    return null;
  }

  void updateBsmSelected(
      CreateCalendarEventController controller, String rRule) {
    bool hasSelected = false;
    for (var element in controller.bsmRepeats) {
      bool isSelected = _sameRRules(rRule, element.value.rRule);

      element.isSelected = isSelected;

      if (isSelected) {
        hasSelected = true;
        controller.selectedRepeatOption.value = element.value;
        if (controller.selectedRepeatOption.value.optionEnum !=
            CalendarEventRepeatOptionEnum.custom) {
          removeCustomBsm(controller);
        }
        break;
      }
    }
    controller.bsmRepeats.last.isSelected = false;

    if (!hasSelected) {
      // thêm option tuỳ chọn lặp lên trên cùng
      insertBsmRepeats(controller);
    }
  }

  void removeCustomBsm(CreateCalendarEventController controller) {
    BottomSheetModel bsm = controller.bsmRepeats.first;
    CalendarEventRepeatOption cero = bsm.value;
    if (cero.optionEnum == CalendarEventRepeatOptionEnum.custom) {
      controller.bsmRepeats.removeAt(0);
    }
  }

  void insertBsmRepeats(CreateCalendarEventController controller) {
    removeCustomBsm(controller);

    controller.bsmRepeats.insert(
      0,
      BottomSheetModel(
        iconAsset: "",
        displayName: controller.selectedRepeatOption.value.displayName,
        value: CalendarEventRepeatOption(
          dateTime: controller.selectedRepeatOption.value.dateTime,
          optionEnum: CalendarEventRepeatOptionEnum.custom,
          rRuleStr: controller.selectedRepeatOption.value.rRule,
        ),
        isSelected: true,
      ),
    );
  }

  void onchangeDateTime(CreateCalendarEventController controller) {
    final DateTime? startDate = controller.calendarController.value?.startDate;
    controller.selectedRepeatOption.value.rRuleModel?.updateRRuleStr(startDate);

    BottomSheetModel? bsm =
        controller.bsmRepeats.firstWhereOrNull((e) => e.isSelected);
    if (bsm != null) {
      CalendarEventRepeatOption cero = bsm.value;
      cero.rRuleModel?.updateRRuleStr(startDate);

      final String rRule = cero.rRuleModel?.rRule ?? "";

      controller.selectedRepeatOption.update((val) {
        val?.update(rRule, startDate: startDate);
      });

      for (var e in controller.bsmRepeats) {
        e.isSelected = false;
      }
    }

    controller.bsmRepeats.clear();
    controller._initBsmRepeatActions();
  }

  void _checkSeletedBsm(CreateCalendarEventController controller) {
    /*
      Nếu không có item nào được selected (trường hợp bấm back)
      => selected item index = 0 nếu là custom
    */

    int numberOfSelected =
        controller.bsmRepeats.where((e) => e.isSelected).length;
    if (numberOfSelected == 0) {
      BottomSheetModel bsm = controller.bsmRepeats.first;
      CalendarEventRepeatOption cero = bsm.value;
      bsm.isSelected = cero.optionEnum == CalendarEventRepeatOptionEnum.custom;
    }
  }

  bool _sameRRules(String firstRRule, String secondRRule) {
    bool sameRRules = true;

    List<String> firstRRuleArray = firstRRule.split(";");
    List<String> secondRRuleArray = secondRRule.split(";");

    if (firstRRuleArray.length != secondRRuleArray.length) {
      sameRRules = false;
    } else {
      for (int i = 0; i < firstRRuleArray.length; ++i) {
        if (!secondRRuleArray.contains(firstRRuleArray[i])) {
          sameRRules = false;
        }
      }
    }

    return sameRRules;
  }
}

mixin HostMixin {
  /*
    Thêm host -> thêm người tham gia tương ứng
    Xóa người tham gia -> xóa host tương ứng
  */

  /// Dựa vào `hostIds` và `selectedHostIds`
  /// để đưa ra ids được thêm vào và ids được xóa đi
  HostDataHandlerModel? handleHostSelected({
    /// ids lấy từ khi get event
    required List<Assignee> hosts,

    /// ids sau khi edit event
    required List<Assignee> selectedHosts,
  }) {
    final hostIds = hosts.map((e) => e.id).toList();

    final diffResult = calculateListDiff<Assignee>(
      hosts,
      selectedHosts,
      detectMoves: true,
      equalityChecker: _compareFieldValues,
    );

    final updateResults = diffResult.getUpdatesWithData();

    if (updateResults.isEmpty) {
      return null;
    }

    final totalHostIds = hostIds.toList();
    final totalHosts = hosts.toList();

    final addedHostIds = <int>[];
    final removedHostIds = <int>[];

    final addedHosts = <Assignee>[];
    final removedHosts = <Assignee>[];

    for (final updateResult in updateResults) {
      updateResult.when(
        insert: (pos, data) {
          if (!hosts.contains(data)) {
            addedHosts.add(data);
            addedHostIds.add(data.id);

            logDebug('added -> $pos, $data');
          } else {
            logDebug('$pos, $data is already exists before');
          }
        },
        remove: (pos, data) {
          removedHosts.add(data);
          removedHostIds.add(data.id);
          logDebug('removed -> $pos, $data');
        },
        change: (pos, oldData, newData) {
          // doNothing
          logDebug('changed -> $pos, $oldData, $newData');
        },
        move: (from, to, data) {
          // doNothing
          logDebug('moved -> $from, $to, $data');
        },
      );
    }

    if (removedHosts.isNotEmpty) {
      for (var e in removedHosts) {
        // nếu có trong list ban đầu (khi fetch event)
        if (hosts.contains(e)) {
          totalHosts.remove(e);
          totalHostIds.remove(e.id);
        }
      }
    }

    totalHosts.addAll(addedHosts);
    totalHostIds.addAll(addedHostIds);

    return HostDataHandlerModel(
      hosts: totalHosts,
      hostIds: totalHostIds,
      //
      addHosts: addedHosts,
      addHostIds: addedHostIds,
      //
      removeHosts: removedHosts,
      removeHostIds: removedHostIds,
    );
  }

  bool _compareFieldValues(Assignee o1, Assignee o2) {
    return o1.id == o2.id;
  }
}

class HostDataHandlerModel {
  HostDataHandlerModel({
    required this.hosts,
    required this.hostIds,
    required this.addHosts,
    required this.addHostIds,
    required this.removeHosts,
    required this.removeHostIds,
  });

  // output bao gồm cả input ban đầu và added
  final List<int> hostIds;
  final List<Assignee> hosts;

  // ids được thêm vào
  final List<int> addHostIds;
  final List<Assignee> addHosts;

  /// ids được xóa đi
  final List<int> removeHostIds;
  final List<Assignee> removeHosts;
}
