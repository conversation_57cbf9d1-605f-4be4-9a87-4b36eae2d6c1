import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'subscription_pricing_controller.dart';
import 'widgets/pricing_card.dart';

/// M<PERSON>n hình hiển thị các gói đăng ký chatbot
class SubscriptionPricingPage extends GetView<SubscriptionPricingController> {
  const SubscriptionPricingPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Set status bar style
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header with status bar and close button
            _buildHeader(),

            // Content
            SingleChildScrollView(
              child: Column(
                children: [
                  // Avatar and Title Section
                  _buildTitleSection(),
                  const SizedBox(height: 40),

                  // Pricing Cards
                  _buildPricingCards(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Spacer(),
        InkWell(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: SvgWidget(
                Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_XMARK_SVG),
          ),
        )
      ],
    );
  }

  Widget _buildTitleSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar
          SizedBox(
            width: 64,
            height: 64,
            child: Image.asset(
              Assets.PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 16),

          // Title and Description
          Text(
            'Gói sử dụng chatbot\nTrợ lý cuộc họp',
            style: textStyle(GPTypography.headingXLarge),
          ),
          const SizedBox(height: 8),
          Text(
            'Bạn có thể hủy bất cứ lúc nào. Bằng việc đăng ký, bạn đồng ý với các điều khoản sử dụng và chính sách của chúng tôi',
            style: textStyle(GPTypography.bodyMedium)
                ?.copyWith(color: GPColor.contentSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingCards() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          const SizedBox(width: 16),
          // Pricing Cards
          ...controller.packages.map((package) {
            final isLast = package == controller.packages.last;
            return Row(
              children: [
                PricingCard(
                  package: package,
                  onContactPressed: () => controller.onContactPressed(package),
                ),
                if (!isLast) const SizedBox(width: 16),
              ],
            );
          }),
          const SizedBox(width: 16),
        ],
      ),
    );
  }
}
