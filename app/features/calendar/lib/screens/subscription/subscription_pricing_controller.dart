import 'package:gp_core/core.dart';
import 'models/subscription_package.dart';

/// Controller cho màn hình subscription pricing
class SubscriptionPricingController extends GetxController {
  /// Danh sách các gói đăng ký
  List<SubscriptionPackage> get packages => SubscriptionPackage.allPackages;

  /// Xử lý khi nhấn nút "Liên hệ ngay"
  void onContactPressed(SubscriptionPackage package) {
    // TODO: Implement contact logic
    logDebug('Contact pressed for package: ${package.name}');

    // <PERSON><PERSON> thể mở dialog, navigate đến màn hình contact, hoặc mở link
    Get.snackbar(
      '<PERSON><PERSON><PERSON> hệ',
      'Bạn đã chọn gói ${package.name}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Xử lý khi nhấn nút đóng
  void onClosePressed() {
    Get.back();
  }
}

/// Binding cho SubscriptionPricingController
class SubscriptionPricingBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SubscriptionPricingController());
  }
}
