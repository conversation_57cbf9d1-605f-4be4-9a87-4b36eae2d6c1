import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import '../models/subscription_package.dart';

/// Widget hiển thị pricing card cho gói đăng ký
class PricingCard extends StatelessWidget {
  final SubscriptionPackage package;
  final VoidCallback? onContactPressed;

  const PricingCard({
    super.key,
    required this.package,
    this.onContactPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 18),
      child: Container(
        width: 300,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: GPColor.linePrimary,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0x1A2F3136),
              offset: const Offset(0, 8),
              blurRadius: 16,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              _buildIcon(),
              const SizedBox(height: 16),

              // Title and Price
              _buildTitleSection(),
              const SizedBox(height: 16),

              // Divider
              Container(
                height: 1,
                color: GPColor.lineTertiary,
              ),
              const SizedBox(height: 16),

              // Features List
              _buildFeaturesList(),
              const SizedBox(height: 16),

              // Contact Button
              _buildContactButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        package.id == 'advanced' ? Icons.star : Icons.diamond,
        color: package.iconColor,
        size: 32,
      ),
    );
  }

  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          package.name,
          style: textStyle(GPTypography.displaySmall),
        ),
        const SizedBox(height: 12),
        Text(
          package.price,
          style: textStyle(GPTypography.headingSmall),
        ),
      ],
    );
  }

  Widget _buildFeaturesList() {
    return Column(
      children: package.features.map((feature) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '👉',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  feature,
                  style: textStyle(GPTypography.bodyMedium),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildContactButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onContactPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              package.isPrimary ? package.iconColor : GPColor.bgTertiary,
          foregroundColor: package.isPrimary
              ? GPColor.contentInversePrimary
              : GPColor.contentPrimary,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
        child: Text(
          'Liên hệ ngay',
          style: textStyle(GPTypography.headingMedium)?.copyWith(
              color: package.isPrimary
                  ? GPColor.contentInversePrimary
                  : GPColor.contentPrimary),
        ),
      ),
    );
  }
}
