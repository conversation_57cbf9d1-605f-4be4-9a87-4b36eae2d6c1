import 'package:flutter/material.dart';

/// Model cho gói đăng ký chatbot
class SubscriptionPackage {
  final String id;
  final String name;
  final String price;
  final String description;
  final String iconPath;
  final Color iconColor;
  final List<String> features;
  final bool isPrimary;

  const SubscriptionPackage({
    required this.id,
    required this.name,
    required this.price,
    required this.description,
    required this.iconPath,
    required this.iconColor,
    required this.features,
    this.isPrimary = false,
  });

  /// Gói <PERSON>âng cao
  static const SubscriptionPackage advanced = SubscriptionPackage(
    id: 'advanced',
    name: 'Nâng cao',
    price: '10 triệu VND/ tháng\nLên đến 50 cuộc họp/ tuần',
    description: '<PERSON><PERSON><PERSON> nâng cao cho doanh nghiệp vừa',
    iconPath: 'star_24dp_E8EAED_FILL0_wght200_GRAD0_opsz20',
    iconColor: Color(0xFFA68200),
    features: [
      'Chatbot được sử dụng tối đa 50 cuộc họp/ tuần để tham gia cuộc họp trong tổ chức của bạn',
      'Tối đa 10 thành viên được quyền thêm chatbot hỗ trợ cuộc họp',
    ],
    isPrimary: true,
  );

  /// Gói Toàn diện
  static const SubscriptionPackage comprehensive = SubscriptionPackage(
    id: 'comprehensive',
    name: 'Toàn diện',
    price: '50 triệu VND/ tháng\nKhông giới hạn cuộc họp',
    description: 'Gói toàn diện cho doanh nghiệp lớn',
    iconPath: 'diamond_24dp_E8EAED_FILL0_wght200_GRAD0_opsz20',
    iconColor: Color(0xFF1A99F4),
    features: [
      'Không giới hạn số lượt cuộc họp có chatbot để tham gia cuộc họp trong tổ chức của bạn',
      'Tối đa 50 thành viên được quyền thêm chatbot hỗ trợ cuộc họp',
    ],
    isPrimary: false,
  );

  /// Danh sách tất cả các gói
  static const List<SubscriptionPackage> allPackages = [
    advanced,
    comprehensive,
  ];
}
