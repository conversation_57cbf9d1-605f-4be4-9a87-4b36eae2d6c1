# Subscription Pricing Screen

Màn hình hiển thị các gói đăng ký chatbot Trợ lý cuộc họp theo design Figma.

## Cấu trúc

```
subscription/
├── models/
│   └── subscription_package.dart      # Model cho gói đăng ký
├── widgets/
│   └── pricing_card.dart             # Widget pricing card
├── subscription_pricing_controller.dart  # GetX controller
├── subscription_pricing_page.dart       # Màn hình chính
├── subscription_deeplink.dart           # Helper để navigate
├── subscription_test_page.dart          # Test page
└── README.md                           # Tài liệu này
```

## Cách sử dụng

### 1. Navigate đến màn hình

```dart
import 'package:gp_feat_calendar/screens/subscription/subscription_deeplink.dart';

// Mở màn hình subscription pricing
SubscriptionDeeplink.openSubscriptionPricing();
```

### 2. Sử dụng trực tiếp với GetX

```dart
import 'package:gp_feat_calendar/routes/router_name.dart';

Get.toNamed(CalendarRouterName.subscriptionPricing);
```

## Features

- ✅ Hiển thị 2 gói đăng ký: Nâng cao và Toàn diện
- ✅ Design theo Figma với header, avatar, title
- ✅ Pricing cards với icon, title, price, features list và button
- ✅ Horizontal scroll cho pricing cards
- ✅ Responsive design
- ✅ GetX controller để quản lý state
- ✅ Navigation và routing đã được setup

## Design

Màn hình được thiết kế theo Figma design:
- Header với status bar và close button
- Avatar và title section
- 2 pricing cards có thể scroll ngang
- Home indicator cho iOS

## Models

### SubscriptionPackage

```dart
class SubscriptionPackage {
  final String id;
  final String name;
  final String price;
  final String description;
  final String iconPath;
  final Color iconColor;
  final List<String> features;
  final bool isPrimary;
}
```

## Testing

### 1. Sử dụng Demo App

Chạy demo app standalone:

```bash
cd app/features/calendar/lib/screens/subscription
flutter run demo_main.dart
```

### 2. Sử dụng Test Page

```dart
import 'package:gp_feat_calendar/screens/subscription/subscription_test_page.dart';

// Navigate đến test page
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const SubscriptionTestPage()),
);
```

### 3. Integrate vào app chính

Thêm button hoặc menu item để navigate:

```dart
ElevatedButton(
  onPressed: () {
    SubscriptionDeeplink.openSubscriptionPricing();
  },
  child: const Text('Xem gói đăng ký'),
)
```