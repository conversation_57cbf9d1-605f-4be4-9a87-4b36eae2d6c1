import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'subscription_deeplink.dart';

/// Test page để test màn hình subscription pricing
class SubscriptionTestPage extends StatelessWidget {
  const SubscriptionTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Subscription Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Test Subscription Pricing Screen',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                SubscriptionDeeplink.openSubscriptionPricing();
              },
              child: const Text('Open Subscription Pricing'),
            ),
          ],
        ),
      ),
    );
  }
}
