import 'package:flutter/material.dart' hide SearchBar;
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/secure_download/gp_secure_download.dart';

import '../widgets/item_with_tooltip.dart';
import 'organization_department_select_list_controller.dart';

class OrganizationDepartmentSelectListPage extends StatefulWidget {
  final OrganizationDepartmentSelectListController controller;

  const OrganizationDepartmentSelectListPage({
    super.key,
    required this.controller,
  });

  @override
  // ignore: library_private_types_in_public_api
  _DepartmentSelectListPageState createState() =>
      // ignore: no_logic_in_create_state
      _DepartmentSelectListPageState(controller);
}

class _DepartmentSelectListPageState
    extends State<OrganizationDepartmentSelectListPage> {
  final OrganizationDepartmentSelectListController controller;

  _DepartmentSelectListPageState(this.controller);

  @override
  void initState() {
    super.initState();
    controller.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: StreamBuilder<List<OrganizationDepartment>>(
          stream: controller.selectedDepartment.stream,
          builder: (context, snapshot) {
            return NestedScrollView(
              controller: ScrollController()
                ..addListener(() {
                  controller.hideTooltip();
                  controller.hideSelectedTooltip();
                }),
              floatHeaderSlivers: true,
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    elevation: 0,
                    leading: const SizedBox(),
                    floating: true,
                    collapsedHeight: controller.allowChangeTree ? 134 : 56,
                    flexibleSpace: Column(
                      children: [
                        if (controller.allowChangeTree)
                          StreamBuilder<OrganizationDepartment?>(
                            stream: controller.selectedTree.stream,
                            builder: (context, snapshot) {
                              return SelectedTreeWidget(
                                treeName: snapshot.data?.name,
                                onTap: controller.pickTree,
                              );
                            },
                          ),
                        if (!controller.allowChangeTree)
                          const SizedBox(height: 10),
                        GPSearchBar(
                          onTap: () {
                            controller.hideTooltip();
                            controller.hideSelectedTooltip();
                          },
                          height: 44,
                          borderRadius: BorderRadius.circular(8),
                          textEditingController:
                              controller.searchTextEditingController,
                          hintText: LocaleKeys.task_search.tr,
                          showClearBtn: true,
                          showBorder: true,
                        ).paddingSymmetric(horizontal: 16),
                      ],
                    ),
                  ),
                  if (snapshot.hasData &&
                      (snapshot.data?.isNotEmpty ?? false) &&
                      (controller.svo.mode !=
                          SelectInviteesOptionsMode.viewOnly))
                    SliverToBoxAdapter(
                      child: Container(
                        color: GPColor.functionAlwaysLightPrimary,
                        // height: 44,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Builder(builder: (context) {
                              List<Widget> listWidget = [];
                              for (int i = 0; i < snapshot.data!.length; i++) {
                                var item = snapshot.data![i];
                                listWidget.add(_SelectedItemWrapper(
                                  department: item,
                                  onTap: item.isEnabled
                                      ? () => controller.onRemove(i)
                                      : null,
                                  tooltip:
                                      controller.tooltipSelectedControllers[
                                          item.id.toString()],
                                ).paddingOnly(bottom: 8));
                              }
                              return Wrap(
                                children: listWidget,
                              ).paddingSymmetric(horizontal: 16);
                            })
                          ],
                        ),
                      ).paddingOnly(top: 12),
                    ),
                ];
              },
              body: StreamBuilder<bool>(
                  stream: controller.isSearching.stream,
                  builder: (context, snapshotSearching) {
                    final isSearching = snapshotSearching.data ?? false;

                    if ((controller.svo.mode ==
                            SelectInviteesOptionsMode.viewOnly) &&
                        (controller.department.value?.hasChildren) == false) {
                      return EmptyView(emptyText: controller.svo.emptyText());
                    }

                    if (isSearching) {
                      return StreamBuilder<List<OrganizationDepartment>>(
                          stream: controller.searchingDepartments.stream,
                          builder: (context, snapshot) {
                            if (snapshot.hasData &&
                                snapshot.data != null &&
                                snapshot.data!.isNotEmpty) {
                              return ListView.builder(
                                keyboardDismissBehavior:
                                    ScrollViewKeyboardDismissBehavior.onDrag,
                                itemCount: snapshot.data!.length,
                                itemBuilder: (context, index) {
                                  final item = snapshot.data![index];
                                  return _SearchingRow(item,
                                      onTap: (dep, p0) =>
                                          controller.onSelect(dep, p0),
                                      tooltip: controller
                                          .tooltipControllers[item.id]);
                                },
                              );
                            } else {
                              return EmptyView(
                                  emptyText: LocaleKeys.error_nodata.tr);
                            }
                          });
                    } else {
                      return StreamBuilder<OrganizationDepartment?>(
                          stream: controller.department.stream,
                          builder: (context, snapshot) {
                            if (snapshot.hasData && snapshot.data != null) {
                              if (snapshot.data!.id.isEmpty) {
                                return EmptyView(
                                    emptyText: controller.svo.emptyText());
                              }
                              return ListView.builder(
                                keyboardDismissBehavior:
                                    ScrollViewKeyboardDismissBehavior.onDrag,
                                itemCount: 1,
                                itemBuilder: (context, index) {
                                  final item = snapshot.data!;
                                  return _content(item);
                                },
                              );
                            }
                            return SizedBox(
                              height: 50,
                              child: Center(
                                child: CircularProgressIndicator(
                                    color: GPColor.workPrimary),
                              ),
                            );
                          });
                    }
                  }),
            );
          }),
    );
  }

  Theme _content(OrganizationDepartment data) {
    final imageUrl =
        Utils.imageThumb(Constants.workspaceThumbnail(), "128x128");
    return Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: Column(
          children: [
            if (data.isNotPrimary)
              if ((controller.svo.mode != SelectInviteesOptionsMode.viewOnly ||
                      controller.containsTreeId) &&
                  controller.svo.showWorkspaceItem)
                ListTile(
                  title: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.network(
                          imageUrl,
                          width: 32,
                          height: 32,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return const SizedBox();
                          },
                          headers: GpSecureDownload.headers(imageUrl),
                        ),
                      ),
                      Flexible(
                        child: Text(
                          data.name,
                          style: textStyle(GPTypography.headingSmall),
                        ).paddingOnly(left: 8),
                      ),
                    ],
                  ),
                  trailing: GPCheckBox(
                    isSelected: data.isSelected,
                    isEnabled: data.isEnabled,
                    onCheckedChanged: (p0) {
                      controller.onSelect(data, p0);
                    },
                  ),
                  contentPadding: const EdgeInsets.only(left: 16),
                  onTap: () {
                    if (data.isEnabled) {
                      controller.onSelect(data, !data.isSelected);
                    }
                  },
                ),
            ...data.children.map((e) => _department(e)),
          ],
        ));
  }

  Widget _department(OrganizationDepartment data) {
    if (controller.svo.mode == SelectInviteesOptionsMode.viewOnly &&
        controller.containsTreeId) {
      return const SizedBox();
    }

    final tooltip = controller.tooltipControllers[data.id];

    final hasSubDepartment = data.children.isNotEmpty;

    final checkBox = GPCheckBox(
      isSelected: data.isSelected,
      isEnabled: data.isEnabled,
      onCheckedChanged: (p0) {
        controller.onSelect(data, p0);
      },
      isCircleShape: controller.svo.selectInviteesPickMode.isPickOne,
    );
    final checkBoxWrapper = tooltip != null
        ? InkWell(onTap: tooltip.onTap, child: checkBox)
        : checkBox;

    if (hasSubDepartment) {
      final content = _TooltipWrapper(
        tooltip: tooltip,
        left: left(data),
        child: Row(
          children: [
            Opacity(
              opacity: hasSubDepartment ? 1 : 0,
              child: Obx(
                () => SvgWidget(
                  data.isExpanded.value
                      ? 'assets/images/ic24-fill-arrowhead-down.png'
                      : 'assets/images/ic24-fill-arrowhead-right.png',
                  color: GPColor.contentSecondary,
                ),
              ),
            ),
            SvgWidget(
              'assets/images/ic24-fill-folder.png',
              width: 24,
              height: 24,
              fit: BoxFit.contain,
              color: GPColor.informativePrimary,
            ),
            Flexible(
              child: Text(
                data.name,
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(fontWeight: FontWeight.w400),
              ).paddingOnly(left: 8),
            ),
          ],
        ),
      );
      return ExpansionTile(
        onExpansionChanged: (value) {
          data.isExpanded.value = value;

          controller.hideTooltip();
          controller.hideSelectedTooltip();
        },
        initiallyExpanded: false,
        tilePadding: EdgeInsets.zero,
        childrenPadding: const EdgeInsets.only(left: 20),
        title: content,
        trailing: checkBoxWrapper,
        children: data.children
            .map((e) => _department(e..level = data.level + 1))
            .toList(),
      );
    } else {
      return _TooltipWrapper(
        tooltip: tooltip,
        left: left(data),
        child: Row(
          children: [
            Opacity(
                opacity: hasSubDepartment ? 1 : 0,
                child: SvgWidget(
                  'assets/images/ic24-fill-arrowhead-down.png',
                  color: GPColor.contentSecondary,
                )),
            SvgWidget(
              'assets/images/ic24-fill-folder.png',
              width: 24,
              height: 24,
              fit: BoxFit.contain,
              color: GPColor.informativePrimary,
            ),
            Expanded(
              child: Text(
                data.name,
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(fontWeight: FontWeight.w400),
              ).paddingOnly(left: 8),
            ),
            checkBoxWrapper
          ],
        ).paddingSymmetric(vertical: 8),
      );
    }
  }

  double left(OrganizationDepartment data) {
    double leftValue = data.level * 20;
    if (leftValue > Get.width / 2) {
      leftValue = (Get.width / 2);
    }
    return leftValue;
  }
}

class _TooltipWrapper extends StatelessWidget {
  const _TooltipWrapper({
    this.tooltip,
    required this.child,
    required this.left,
  });

  final AssigneeTooltipModel? tooltip;
  final Widget child;
  final double left;

  @override
  Widget build(BuildContext context) {
    return tooltip != null
        ? AssigneeTooltipWidget(
            tooltipController: tooltip!.tooltipController,
            isAddedByAdminStr: tooltip?.isAddedByAdminStr,
            dynamicLeft: true,
            dynamicLeftValue: RxDouble(left),
            arrowTipDistance: 18,
            child: child)
        : child;
  }
}

class _SearchingRow extends StatelessWidget {
  const _SearchingRow(
    this.department, {
    required this.onTap,
    this.tooltip,
  });

  final OrganizationDepartment department;
  final void Function(OrganizationDepartment, bool) onTap;
  final AssigneeTooltipModel? tooltip;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: department.isEnabled
          ? () => onTap(department, !department.isSelected)
          : null,
      child: WidgetWithTooltip(
        avatarWidget: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              fit: FlexFit.loose,
              child: Text(
                department.name,
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(fontWeight: FontWeight.w400),
              ).paddingOnly(left: 8),
            ),
            GPCheckBox(
              isSelected: department.isSelected,
              isEnabled: department.isEnabled,
              onCheckedChanged: (p0) => onTap.call(department, p0),
            ),
          ],
        ),
        tooltip: tooltip,
        arrowTipDistance: 20,
        dynamicLeft: false,
      ),
    );
  }
}

class _SelectedItem extends StatelessWidget {
  final OrganizationDepartment department;
  final void Function()? onTap;

  const _SelectedItem({
    required this.department,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: Container(
          padding: const EdgeInsets.all(8),
          // height: 32,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: GPColor.blueLight,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  department.name,
                  style: textStyle(GPTypography.bodyMedium)
                      ?.copyWith(color: GPColor.blueDark, height: 1),
                ),
              ),
              const SizedBox(width: 8),
              if (department.isEnabled)
                SizedBox(
                    width: 16,
                    height: 16,
                    child: SvgWidget(
                      'assets/images/svg/ic16-fill-xmark.svg',
                      color: GPColor.blueDark,
                      fit: BoxFit.scaleDown,
                    )),
            ],
          ),
        ),
      ),
    );
  }
}

class _SelectedItemWrapper extends StatelessWidget {
  const _SelectedItemWrapper({
    required this.department,
    this.onTap,
    this.tooltip,
  });

  final OrganizationDepartment department;
  final void Function()? onTap;

  final AssigneeTooltipModel? tooltip;

  @override
  Widget build(BuildContext context) {
    return WidgetWithTooltip(
      avatarWidget: _SelectedItem(department: department, onTap: onTap),
      tooltip: tooltip,
      dynamicLeftValue: rxDepartmnentTooltipLeft,
      arrowTipDistance: 22,
    );
  }
}

class SelectedTreeWidget extends StatelessWidget {
  const SelectedTreeWidget({this.treeName, this.onTap, super.key});

  final String? treeName;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: GPColor.lineTertiary),
            borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.all(8),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: GPColor.blueLighter),
                child: Center(
                    child: SvgWidget(
                  'assets/images/ic20-fill-organizational-chart.svg',
                  color: GPColor.blueDark,
                )),
              ),
              const SizedBox(width: 12),
              Text(
                treeName ?? "",
                style: textStyle(GPTypography.headingSmall),
              ),
              Flexible(
                child: Align(
                  alignment: Alignment.centerRight,
                  child: SvgWidget(
                    'assets/images/ic20-fill-arrowhead-down.svg',
                    color: GPColor.contentSecondary,
                  ),
                ),
              ),
            ]),
      ),
    );
  }
}
