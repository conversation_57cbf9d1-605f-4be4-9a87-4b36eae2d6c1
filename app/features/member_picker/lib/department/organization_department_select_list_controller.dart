import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_member_picker/picker_mixin.dart';
import 'package:gp_feat_member_picker/selected_list/select_invitees_controller.dart';

import '../invitees_list/widget/limit_dialog.dart';
import '../version/tooltip_mixin.dart';
import 'list_tree_bottom_sheet.dart';

class OrganizationDepartmentSelectListController extends BaseListController
    with
        OrgChatConfigTab,
        OrgChatDepartment,
        TooltipMixin,
        HasChangeSelectedFlag
    implements ArgumentInitialMixin {
  final _service = OrganizationAPI();

  final isSearching = BehaviorSubject<bool>.seeded(false);
  final searchingDepartments =
      BehaviorSubject<List<OrganizationDepartment>>.seeded([]);
  List<OrganizationDepartment> _flattenList = [];
  List<OrganizationDepartment> orgTree = [];
  final selectedTree = BehaviorSubject<OrganizationDepartment?>.seeded(null);
  final department = BehaviorSubject<OrganizationDepartment?>.seeded(null);
  final selectedDepartment =
      BehaviorSubject<List<OrganizationDepartment>>.seeded([]);

  final searchTextEditingController = TextEditingController();

  bool get allowChangeTree => svo.allowChangeTree;

  bool get containsTreeId =>
      svo.selectedDepartmentIds?.contains(svo.treeId) ?? false;

  OrganizationDepartmentSelectListController({GPConnection? gpConnection})
      : super(gpConnection ?? GPConnectionConcrete());

  void setOnPickModeListener(Function onPickOneCallback) {
    this.onPickOneCallback = onPickOneCallback;
  }

  void initState() {
    if (department.value == null) {
      _getDepartments();
    }
    department.listen((value) {
      _updateSearchMatches(_searchKeyword);
    });

    searchTextEditingController.addListener(() {
      final text = searchTextEditingController.text;
      searchTextValueChanged(text);
    });
  }

  void pickTree() async {
    final result =
        await ListTreeBottomSheet.instance.show(selectedTree.value, orgTree);
    if (result != null && selectedTree.value != result) {
      selectedTree.value = result;
      svo.treeId = result.id;
      selectedDepartment.value = [];
      _getDepartments();
    }
  }

  List<OrganizationDepartment> _tempFlat = [];

  List<OrganizationDepartment> _flatOne(
      OrganizationDepartment organizationDepartment) {
    if (organizationDepartment.isNotPrimary) {
      _tempFlat.add(organizationDepartment);
    }
    for (var item in organizationDepartment.children) {
      _flatOne(item);
    }
    return _tempFlat;
  }

  void _getDepartments() async {
    try {
      final OrganizationDepartment? rootDepartment;
      final OrganizationDepartmentResponse response =
          await _service.getDepartments();

      orgTree.clear();
      for (var val in response.data) {
        orgTree.add(val.copyWith());
      }
      if (hasTreeId) {
        // Hiển thị tree theo tree_id truyền vào
        rootDepartment =
            response.data.firstWhereOrNull(((e) => e.id == dTreeId));
        department.add(rootDepartment);
        selectedTree.value = rootDepartment?.copyWith();
      } else {
        if (selectedDepartment.value.isNotEmpty) {
          // Hiển thị tree theo department đã được chọn
          final id = selectedDepartment.value.first.treeId ??
              selectedDepartment.value.first.id;
          rootDepartment =
              response.data.firstWhereOrNull((element) => element.id == id) ??
                  response.data
                      .firstWhereOrNull((element) => element.isPrimary == true);
        } else {
          // Không có deparment nào được chọn sẽ chọn theo tree là primary
          rootDepartment = response.data
              .firstWhereOrNull((element) => element.isPrimary == true);
        }
        selectedTree.value = rootDepartment?.copyWith() ?? orgTree.first;
        svo.treeId = selectedTree.value?.id;
        department.add(rootDepartment);
      }

      if (allowPickAllDepartment) {
        department.value?.name = pickAllDepartmentTitle;
      }

      if (svo.mode == SelectInviteesOptionsMode.viewOnly) {
        department.value?.isEnabled = false;
        department.value?.isSelected = true;

        department.add(department.value);
      }

      if (rootDepartment == null) {
        // show empty error
        department.add(OrganizationDepartment(id: "", name: "", children: []));
        return;
      }

      // flat
      _tempFlat = [];
      _flatOne(rootDepartment);

      _flattenList = _tempFlat.toList();

      // fill selected items
      fillSelectedItems();

      _updateNotRemovable();
    } catch (e, trace) {
      logDebug(trace);
      handleError(e, trace);
    }
  }

  void fillSelectedItems() {
    if (selectedDepartment.value.isNotEmpty) {
      final selected = _flattenList
          .where((element) =>
              selectedDepartment.value.any((sd) => sd.id == element.id))
          // selectedDepartment.value.contains(element))
          .toList();
      if (selected.isEmpty) {
        _onSelect(selectedDepartment.value.first, true);
      } else {
        for (final element in selected) {
          _onSelect(element, true);
        }
      }
    }
  }

  void _onSelect(OrganizationDepartment data, bool p0) {
    data.isSelected = p0;
    for (var element in data.children) {
      if (svo.selectedDepartmentIds?.isEmpty == true) {
        if (!(svo.selectedDepartmentIds?.contains(element.id) == true)) {
          _updateIsSelected(data: element, isSelected: p0, isEnabled: !p0);
        }
      }
    }
    department.add(department.value);

    // selected section
    if (department.value != null) {
      _tempListSelectedAndEnabled = [];
      _findListSelectedAndEnabled(department.value!);
      if (allowPickAllDepartment) {
        if (_tempListSelectedAndEnabled.isNotEmpty &&
            _tempListSelectedAndEnabled.first.isPrimary == true) {
          _tempListSelectedAndEnabled.first.id = Constants.workspaceId();
        }
      }
      selectedDepartment.add(_tempListSelectedAndEnabled);
    }
  }

  void onSelect(OrganizationDepartment data, bool p0) {
    // Kiểm tra giới hạn chọn cho tab department khi đang chọn (p0 = true)
    if (p0) {
      final tabLimit = SelectInviteeTabs.department.getMaxSelectionLimit(svo);
      if (tabLimit != null && selectedDepartment.value.length >= tabLimit) {
        Popup.instance.showBottomSheet(LimitDialog(
            title:
                "${LocaleKeys.memberPicker_limit_title.tr} ${SelectInviteeTabs.department.title.toLowerCase()}",
            content:
                "${LocaleKeys.memberPicker_limit_description.tr} $tabLimit ${SelectInviteeTabs.department.title.toLowerCase()}"));
        return;
      }
    }

    _onSelect(data, p0);
    Get.find<SelectInviteesController>().checkHasAnySelectedItem();

    if (p0) {
      onPickOneCallback?.call();
    }

    _updateNotRemovable();

    hasChangeSelected = true;
  }

  List<OrganizationDepartment> _tempListSelectedAndEnabled = [];
  void _findListSelectedAndEnabled(OrganizationDepartment data) {
    if (data.isSelected && data.isEnabled) {
      _tempListSelectedAndEnabled.add(data);
    }
    for (var element in data.children) {
      _findListSelectedAndEnabled(element);
    }
  }

  void _updateIsSelected({
    required OrganizationDepartment data,
    required bool isSelected,
    required bool isEnabled,
    Function(OrganizationDepartment)? handleEachItem,
  }) {
    data.isSelected = isSelected;
    data.isEnabled = isEnabled;

    handleEachItem?.call(data);

    for (var element in data.children) {
      _updateIsSelected(
        data: element,
        isEnabled: isEnabled,
        isSelected: isSelected,
        handleEachItem: handleEachItem,
      );
    }
  }

  void onRemove(int index) {
    final data = selectedDepartment.value[index];
    _onSelect(data, false);

    _updateNotRemovable();

    hasChangeSelected = true;
  }

  String _searchKeyword = '';
  void searchTextValueChanged(String text) {
    _searchKeyword = text;

    // update searching flag
    isSearching.add(text.isNotEmpty);

    _updateSearchMatches(text);
  }

  void _updateSearchMatches(String keyword) {
    final matches = _flattenList
        .where(
          (element) => TiengViet.parse(element.name)
              .toLowerCase()
              .contains(TiengViet.parse(keyword).toLowerCase()),
        )
        .toList();
    searchingDepartments.add(matches);
  }

  void removeAllSeleted() {
    for (var department in selectedDepartment.value) {
      _onSelect(department, false);
    }
    selectedDepartment.add([]);

    _updateNotRemovable();

    hasChangeSelected = true;
  }

  void _updateNotRemovable() {
    void updateNotRemovableList(
      List<String>? input, {
      Function(OrganizationDepartment)? handleEachItem,
    }) {
      if (input?.isNotEmpty == true) {
        for (var e in input!) {
          final d = _flattenList
              .firstWhereOrNull((element) => element.id.toString() == e);

          if (d != null) {
            _updateIsSelected(
                data: d,
                isEnabled: false,
                isSelected: true,
                handleEachItem: handleEachItem);

            if (!selectedDepartment.value.contains(d)) {
              selectedDepartment.value.add(d);
            }
          }
        }

        selectedDepartment.add(selectedDepartment.value);
      } else {
        if (svo.mode == SelectInviteesOptionsMode.viewOnly) {
          department.value?.id = "";
        }
      }
    }

    if (svo.mode == SelectInviteesOptionsMode.viewOnly) {
      updateNotRemovableList(svo.selectedDepartmentIds);

      _flattenList.clear();
      _flattenList.addAll(selectedDepartment.value);

      department.value?.children.clear();
      department.value?.children.addAll(selectedDepartment.value);
    } else {
      updateNotRemovableList(svo.notRemovableDepartmentIds);

      // thread được thêm bởi admin không thể bỏ chọn
      updateNotRemovableList(svo.departmentAddedByAdminIds,
          handleEachItem: (p0) {
        addedTooltipByItem(
          p0.id.toString(),
          isAddedByAdminStr: svo.isAddedByAdminStr(),
        );
      });
    }

    department.add(department.value);
  }

  @override
  void initArguments(SelectInviteesOptions arg) {
    svo = arg;
  }
}
