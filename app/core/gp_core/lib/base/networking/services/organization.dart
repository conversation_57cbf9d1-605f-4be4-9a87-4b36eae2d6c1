import 'package:gp_core/base/networking/base/api.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/models/orgchat/organization_department.dart';
import 'package:gp_core/models/orgchat/organization_role.dart';

class OrganizationAPI {
  // final ApiService _service = ApiService(Constants.organizationDomain,
  //     gpDomainChecker: GPDomainChecker.organizationChart);
  final ApiService _serviceV3 = ApiService(Constants.organizationDomainV3,
      gpDomainChecker: GPDomainChecker.organizationChartV3);

  Future<OrganizationDepartmentResponse> getDepartments() async {
    final response = await _serviceV3.getData(endPoint: '/department');
    return OrganizationDepartmentResponse.fromJson(response.data);
  }

  Future<List<OrganizationRole>> getRoles() async {
    final response = await _serviceV3.getData(endPoint: '/role');
    if (response.data['data'] is List) {
      return (response.data['data'] as List)
          .map((e) => OrganizationRole.fromJson(e))
          .toList();
    }
    return [];
  }
}
