import 'package:gp_core/base/models/base_models.dart';
import 'package:gp_core/base/networking/base/api.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/models/conversation.dart';
import 'package:gp_core/models/participant.dart';

class ChatAPI {
  final ApiService service =
      ApiService(Constants.chatDomain, gpDomainChecker: GPDomainChecker.chat);

  Future<List<Conversation>> getListConversation({
    int? lastId,
    int pageSize = 10,
  }) async {
    var query = {'page_size': pageSize};
    if (lastId != null) {
      query['last_id'] = lastId;
    }
    final response = await service.getData(endPoint: '/threads', query: query);
    final result = List<Conversation>.from(
        response.data["data"].map((x) => Conversation.fromJson(x))).toList();
    return result;
  }

  Future<Conversation> getConversation({
    required int id,
  }) async {
    final response = await service.getData(endPoint: '/threads/$id');
    final result = Conversation.fromJson(response.data["data"]);
    return result;
  }

  Future<List<Participant>> getListParticipants({
    required String threadId,
    String? lastId,
    int pageSize = 100,
  }) async {
    var query = {
      'page_size': pageSize,
      'thread_id': threadId,
    };
    if (lastId != null) query['last_id'] = lastId;
    final response =
        await service.getData(endPoint: '/participants', query: query);
    return List.from(response.data['data'].map((x) => Participant.fromJson(x)));
  }

  Future<Conversation?> getConversationInfo(
      {required int id, String? passcode}) async {
    final Map<String, String> query =
        passcode != null ? {'pass_code': passcode} : {};

    final response =
        await service.getData(endPoint: '/threads/$id', query: query);

    ApiResponse<Conversation> result = ApiResponse.fromJson(
      response.data,
      (jsonData) => Conversation.fromJson(jsonData),
    );

    return result.data;
  }
}
