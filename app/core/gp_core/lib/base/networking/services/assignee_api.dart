import 'package:gp_core/base/models/base_models.dart';
import 'package:gp_core/base/networking/base/api.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/models/assignee.dart';

const _searchUser = '/search-user';
const _searchUserInviteWorkspace = '/search-user-invite-workspace';

class AssigneeApi {
  final ApiService _service = ApiService(Constants.searchDomain,
      gpDomainChecker: GPDomainChecker.search);
  final ApiService _searchGoService = ApiService(Constants.searchGoDomain,
      gpDomainChecker: GPDomainChecker.search);

  Future<ListAPIResponse<Assignee>> getAssignees({
    required String q,
    bool onlyCurrentWorkspace = false,
    String nextLink = "",
    int limit = 10,
    bool isSearching = false,
    String groupId = "",
    String threadId = "",
  }) async {
    var params = {"limit": limit.toString()};
    var endpoint = _searchUserInviteWorkspace;
    // search users
    // for specific workspace

    /*
      08/07/2025
      ios đang để default `onlyCurrentWorkspace` = true
      Khi thêm thành viên threadId != null

      Ở chat, trên mobile:
      - Thêm thành viên vào nhóm chat mới:
        - ở page đầu tiên: đang dùng search-go/search-user-invite-workspace
        - khi search đang dùng search-user
      - Thêm thành viên vào nhóm chat đã có (có thread-id)
        - ở page đầu tiên: đang dùng search-user
        - khi search đang dùng search-user
    */
    if (threadId.isNotEmpty) {
      endpoint = _searchUser;
      params['thread_id'] = threadId;
    } else {
      if (onlyCurrentWorkspace) {
        endpoint = _searchUserInviteWorkspace;
        params['workspace_id'] = Constants.workspaceId();
        if (groupId.isNotEmpty) {
          params['group_id'] = groupId;
        }
      } else if (isSearching) {
        endpoint = _searchUser;
        params['thread_id'] = threadId;
      }
    }
    // else {
    //   endpoint = '/search-user';
    //   // threadId chỉ áp dụng khi search-user
    //   if (threadId.isNotEmpty) {
    //     params['thread_id'] = threadId;
    //   }
    // }
    if (nextLink.isNotEmpty) params.addAll(Uri.splitQueryString(nextLink));
    // nextLink contains `q=`, this will renew `q=`

    if (q.isNotEmpty) params.addAll({"q": q});
    // new search domain for `_searchUserInviteWorkspace`
    if (endpoint == _searchUserInviteWorkspace) {
      if (q.isEmpty) {
        params.addAll({"q": "\"\""});
      }
      final response =
          await _searchGoService.getData(endPoint: endpoint, query: params);
      ListAPIResponse<Assignee> result = ListAPIResponse.fromJson(
        response.data,
        (jsonData) => Assignee.fromJson(jsonData),
      );
      return result;
    }

    final response = await _service.getData(endPoint: endpoint, query: params);
    ListAPIResponse<Assignee> result = ListAPIResponse.fromJson(
      response.data,
      (jsonData) => Assignee.fromJson(jsonData),
    );
    return result;
  }
}
