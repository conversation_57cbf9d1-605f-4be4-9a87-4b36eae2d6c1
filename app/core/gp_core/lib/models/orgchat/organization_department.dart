import 'dart:convert';

import 'package:gp_shared_dep/gp_shared_dep.dart';

OrganizationDepartmentResponse organizationDepartmentFromJson(String str) =>
    OrganizationDepartmentResponse.fromJson(json.decode(str));

String organizationDepartmentToJson(OrganizationDepartmentResponse data) =>
    json.encode(data.toJson());

class OrganizationDepartmentResponse {
  OrganizationDepartmentResponse({
    required this.data,
  });

  List<OrganizationDepartment> data;

  factory OrganizationDepartmentResponse.fromJson(Map<String, dynamic> json) =>
      OrganizationDepartmentResponse(
        data: json["data"] == null
            ? []
            : List<OrganizationDepartment>.from(
                json["data"].map((x) => OrganizationDepartment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class OrganizationDepartment with _OrganizationDepartmentExt {
  OrganizationDepartment({
    required this.id,
    required this.name,
    required this.children,
    this.isPrimary,
    this.threadId,
    this.treeId,
  });

  String id;
  String name;
  String? groupId;
  String? treeId;
  String? threadId;
  bool? isPrimary;
  List<OrganizationDepartment> children;

  bool get hasChildren => children.isNotEmpty;

  bool get isNotPrimary => isPrimary != true;

  factory OrganizationDepartment.fromJson(Map<String, dynamic> json) =>
      OrganizationDepartment(
          id: json["id"],
          name: json["name"],
          isPrimary: json["is_primary"],
          threadId: json["thread_id"],
          children: json["children"] == null
              ? []
              : List<OrganizationDepartment>.from(json["children"]
                  .map((x) => OrganizationDepartment.fromJson(x))),
          treeId: json["tree_id"]);

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "tree_id": treeId,
        "children": List<dynamic>.from(children.map((x) => x.toJson())),
      };

  OrganizationDepartment copyWith() =>
      OrganizationDepartment(id: id, name: name, children: children);

  @override
  bool operator ==(Object other) {
    if (other is OrganizationDepartment) {
      return id == other.id && name == other.name;
    }
    return false;
  }
}

mixin _OrganizationDepartmentExt {
  RxBool isExpanded = false.obs;

  bool isSelected = false;
  bool isEnabled = true;

  int level = 1;
}
