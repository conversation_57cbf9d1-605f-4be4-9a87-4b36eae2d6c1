# _internal_variables_:
#   - &gp_default_ref "feat/3.13.6"

name: gp_core
description: Gapo Core
# publish_to: "none"
version: 4.0.0

environment:
  sdk: ">=3.6.0 <4.0.0"
  flutter: 3.29.1
resolution: workspace

dependencies:
  flutter:
    sdk: flutter
  # flutter_localizations:
  #   sdk: flutter

  # WIDGETS
  flutter_sticky_header: ^0.7.0
  # pull_to_refresh
  # 2.0.0, to remove widget binding ! on flutter 3
  # https://github.com/peng8350/flutter_pulltorefresh/pull/589
  pull_to_refresh:
    git:
      url: https://github.com/miquelbeltran/flutter_pulltorefresh

  cached_network_image: ^3.1.0
  expandable: ^5.0.1
  syncfusion_flutter_core: 23.1.40

  # UTILITIES
  # package_info: ^2.0.2
  url_launcher: 6.3.1
  
  # share: ^2.0.4
  rxdart: 0.28.0
  tiengviet: ^1.0.0

  gp_shared_dep:  
    git:
      url: **********************:flutter/components/gp_shared_dep.git
      ref: "develop"
    # path: ../../shared/gp_shared_dep

  flutter_quill:
    git:
      url: **********************:flutter/rtfeditor.git
      ref: "feat/3.22.2"

  gp_dio_log:
    git:
      url: **********************:flutter/gp_dio_log.git
      ref: "feat/3.22.2"

  # msal_flutter:
  #   git:
  #     url: **********************:flutter/msal-flutter.git
  #     ref: "master"

  detectable_text_field: ^3.0.2
  shimmer: ^3.0.0
  loadmore: ^2.0.1

  # File picker
  file_picker: ^9.2.1
  # better_open_file: ^3.6.3
  open_filex: 4.7.0
  android_id: ^0.4.0

  video_thumbnail:
    git:
      url: **********************:flutter/video_thumbnail.git
      ref: master

  flutter_video_info: ^1.2.0

  # gallery_saver:
  #   git:
  #     url: https://github.com/hoangthai9217/gallery_saver
  #     ref: 12ba62d

  gallery_saver_plus: 3.2.4
  image_gallery_saver_plus: 4.0.1

  # image_save: ^5.0.0

  # video_player:
  #   git:
  #     url: **********************:flutter/plugins.git
  #     path: packages/video_player/video_player
  #     ref: exo/2.17.1
  video_player: 2.9.5

  # super_clipboard:
    # git:
    #   url: **********************:flutter/utils/gp_super_clipboard.git
    #   ref: "develop"

  connectivity_plus: ^6.0.3

  photo_view: ^0.15.0

  path_provider:
  path:

  # Networking
  dio: ^5.2.1+1

  emoji_picker_flutter: 4.3.0
  flutter_keyboard_visibility: ^6.0.0

  snap_scroll_physics: ^0.0.1+3
  flutter_widget_from_html_core: ^0.16.0

  flutter_secure_storage: ^9.0.0

  time: ^2.1.3

  timezone: 0.10.0

  stack_trace: ^1.11.0
  
  app_settings: ^5.1.1

  flutter_udid: ^4.0.0

  sentry: 8.14.0
  sentry_flutter: 8.14.0
  sentry_logging: 8.14.0
  sentry_dio: 8.14.0
  logging: ^1.0.2
  # flutter_vlc_player: ^7.4.2

  image: ^4.1.7
  # image_gallery_saver: ^2.0.3
  webview_flutter: ^4.10.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.4.6
  
  flutter_lints: ^5.0.0

  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  